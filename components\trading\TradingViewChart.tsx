import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { analyzeChartWithGermAyori, formatAnalysisForDisplay, GermAyoriAnalysis } from '@/services/germAyoriStrategy';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import React, { useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { WebView } from 'react-native-webview';

const { width, height } = Dimensions.get('window');

interface TradingViewChartProps {
  symbol?: string;
  interval?: string;
  theme?: 'light' | 'dark';
  onSymbolChange?: (symbol: string) => void;
}

export const TradingViewChart: React.FC<TradingViewChartProps> = ({
  symbol = 'FX:XAUUSD',  // XAUUSD como símbolo por defecto
  interval = '15',       // 15 minutos por defecto
  theme = 'dark',
  onSymbolChange
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  const webViewRef = useRef<WebView>(null);

  const [currentSymbol, setCurrentSymbol] = useState(symbol);
  const [currentInterval, setCurrentInterval] = useState(interval);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<GermAyoriAnalysis | null>(null);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [showSymbolPicker, setShowSymbolPicker] = useState(false);

  // Símbolos populares organizados por categorías
  const popularSymbols = {
    crypto: [
      { symbol: 'BINANCE:BTCUSDT', name: 'Bitcoin' },
      { symbol: 'BINANCE:ETHUSDT', name: 'Ethereum' },
      { symbol: 'BINANCE:ADAUSDT', name: 'Cardano' },
      { symbol: 'BINANCE:SOLUSDT', name: 'Solana' },
      { symbol: 'BINANCE:DOTUSDT', name: 'Polkadot' },
      { symbol: 'BINANCE:LINKUSDT', name: 'Chainlink' },
      { symbol: 'BINANCE:AVAXUSDT', name: 'Avalanche' },
      { symbol: 'BINANCE:MATICUSDT', name: 'Polygon' }
    ],
    forex: [
      { symbol: 'FX:XAUUSD', name: '🥇 ORO/USD' },     // PAR MÁS IMPORTANTE
      { symbol: 'FX:XAGUSD', name: '🥈 PLATA/USD' },
      { symbol: 'FX:EURUSD', name: 'EUR/USD' },
      { symbol: 'FX:GBPUSD', name: 'GBP/USD' },
      { symbol: 'FX:USDJPY', name: 'USD/JPY' },
      { symbol: 'FX:AUDUSD', name: 'AUD/USD' },
      { symbol: 'FX:USDCAD', name: 'USD/CAD' },
      { symbol: 'FX:NZDUSD', name: 'NZD/USD' },
      { symbol: 'FX:USDCHF', name: 'USD/CHF' },
      { symbol: 'FX:EURGBP', name: 'EUR/GBP' },
      { symbol: 'FX:EURJPY', name: 'EUR/JPY' },
      { symbol: 'FX:GBPJPY', name: 'GBP/JPY' }
    ],
    stocks: [
      { symbol: 'NASDAQ:AAPL', name: 'Apple' },
      { symbol: 'NASDAQ:GOOGL', name: 'Google' },
      { symbol: 'NASDAQ:MSFT', name: 'Microsoft' },
      { symbol: 'NASDAQ:TSLA', name: 'Tesla' },
      { symbol: 'NASDAQ:AMZN', name: 'Amazon' },
      { symbol: 'NASDAQ:META', name: 'Meta' },
      { symbol: 'NYSE:JPM', name: 'JPMorgan' }
    ],
    commodities: [
      { symbol: 'COMEX:GC1!', name: 'Oro' },
      { symbol: 'NYMEX:CL1!', name: 'Petróleo WTI' },
      { symbol: 'COMEX:SI1!', name: 'Plata' },
      { symbol: 'CBOT:ZW1!', name: 'Trigo' },
      { symbol: 'NYMEX:NG1!', name: 'Gas Natural' }
    ]
  };

  const timeframes = [
    { value: '1', label: '1m' },
    { value: '5', label: '5m' },
    { value: '15', label: '15m' },
    { value: '60', label: '1H' },
    { value: '240', label: '4H' },
    { value: '1D', label: '1D' },
    { value: '1W', label: '1W' }
  ];

  // HTML para TradingView Widget
  const getTradingViewHTML = () => {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body { margin: 0; padding: 0; background: ${theme === 'dark' ? '#1e1e1e' : '#ffffff'}; }
            #tradingview_widget { width: 100%; height: 100vh; }
        </style>
    </head>
    <body>
        <div id="tradingview_widget"></div>
        <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
        <script type="text/javascript">
            new TradingView.widget({
                "width": "100%",
                "height": "100%",
                "symbol": "${currentSymbol}",
                "interval": "${currentInterval}",
                "timezone": "Etc/UTC",
                "theme": "${theme}",
                "style": "1",
                "locale": "es",
                "toolbar_bg": "${theme === 'dark' ? '#1e1e1e' : '#ffffff'}",
                "enable_publishing": false,
                "hide_top_toolbar": false,
                "hide_legend": false,
                "save_image": false,
                "container_id": "tradingview_widget",
                "studies": [
                    "Volume@tv-basicstudies",
                    "RSI@tv-basicstudies",
                    "MACD@tv-basicstudies"
                ],
                "show_popup_button": true,
                "popup_width": "1000",
                "popup_height": "650",
                "allow_symbol_change": true,
                "details": true,
                "hotlist": true,
                "calendar": true,
                "studies_overrides": {},
                "overrides": {
                    "paneProperties.background": "${theme === 'dark' ? '#1e1e1e' : '#ffffff'}",
                    "paneProperties.vertGridProperties.color": "${theme === 'dark' ? '#2a2a2a' : '#e1e1e1'}",
                    "paneProperties.horzGridProperties.color": "${theme === 'dark' ? '#2a2a2a' : '#e1e1e1'}",
                    "symbolWatermarkProperties.transparency": 90,
                    "scalesProperties.textColor": "${theme === 'dark' ? '#ffffff' : '#000000'}",
                    "mainSeriesProperties.candleStyle.upColor": "#00ff88",
                    "mainSeriesProperties.candleStyle.downColor": "#ff4757",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#00ff88",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#ff4757",
                    "mainSeriesProperties.candleStyle.wickUpColor": "#00ff88",
                    "mainSeriesProperties.candleStyle.wickDownColor": "#ff4757"
                }
            });

            // Comunicación con React Native
            window.ReactNativeWebView = window.ReactNativeWebView || {};
            
            // Función para capturar screenshot (simulada)
            window.captureChart = function() {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'CAPTURE_CHART',
                    symbol: "${currentSymbol}",
                    interval: "${currentInterval}"
                }));
            };

            // Detectar cambios de símbolo
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'SYMBOL_CHANGED') {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'SYMBOL_CHANGED',
                        symbol: event.data.symbol
                    }));
                }
            });
        </script>
    </body>
    </html>
    `;
  };

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      
      if (data.type === 'CAPTURE_CHART') {
        handleAnalyzeChart();
      } else if (data.type === 'SYMBOL_CHANGED') {
        setCurrentSymbol(data.symbol);
        if (onSymbolChange) {
          onSymbolChange(data.symbol);
        }
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  const handleAnalyzeChart = async () => {
    try {
      setIsAnalyzing(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Simular captura de pantalla y análisis
      // En una implementación real, aquí capturaríamos la pantalla del WebView
      const mockImageBase64 = 'mock_chart_data';
      
      const result = await analyzeChartWithGermAyori(mockImageBase64);
      
      // Agregar información del símbolo actual
      if (result.entry) {
        result.entry.symbol = currentSymbol.split(':')[1] || currentSymbol;
      }
      
      setAnalysis(result);
      setShowAnalysisModal(true);

      Haptics.notificationAsync(
        result.isValidEntry 
          ? Haptics.NotificationFeedbackType.Success 
          : Haptics.NotificationFeedbackType.Warning
      );

    } catch (error) {
      Alert.alert('Error', 'No se pudo analizar el gráfico. Inténtalo de nuevo.');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const changeSymbol = (newSymbol: string) => {
    setCurrentSymbol(newSymbol);
    setShowSymbolPicker(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (onSymbolChange) {
      onSymbolChange(newSymbol);
    }
  };

  const changeTimeframe = (newInterval: string) => {
    setCurrentInterval(newInterval);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getSignalColor = (signal?: string) => {
    switch (signal) {
      case 'BUY': return '#00FF88';
      case 'SELL': return '#FF4757';
      default: return '#FFD700';
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header con controles */}
      <View style={[styles.header, { backgroundColor: colors.card }]}>
        <TouchableOpacity
          style={styles.symbolButton}
          onPress={() => setShowSymbolPicker(true)}
        >
          <ThemedText style={styles.symbolText}>
            {currentSymbol.split(':')[1] || currentSymbol}
          </ThemedText>
          <Ionicons name="chevron-down" size={16} color={colors.text} />
        </TouchableOpacity>

        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.timeframeContainer}
        >
          {timeframes.map((tf) => (
            <TouchableOpacity
              key={tf.value}
              style={[
                styles.timeframeButton,
                {
                  backgroundColor: currentInterval === tf.value 
                    ? colors.primary 
                    : 'transparent'
                }
              ]}
              onPress={() => changeTimeframe(tf.value)}
            >
              <ThemedText
                style={[
                  styles.timeframeText,
                  {
                    color: currentInterval === tf.value 
                      ? '#FFFFFF' 
                      : colors.text
                  }
                ]}
              >
                {tf.label}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <TouchableOpacity
          style={[
            styles.analyzeButton,
            { 
              backgroundColor: isAnalyzing ? colors.secondary : '#FFD700',
              opacity: isAnalyzing ? 0.7 : 1
            }
          ]}
          onPress={handleAnalyzeChart}
          disabled={isAnalyzing}
        >
          {isAnalyzing ? (
            <ActivityIndicator size="small" color="#000000" />
          ) : (
            <Ionicons name="analytics" size={20} color="#000000" />
          )}
        </TouchableOpacity>
      </View>

      {/* TradingView Chart */}
      <View style={styles.chartContainer}>
        <WebView
          ref={webViewRef}
          source={{ html: getTradingViewHTML() }}
          style={styles.webview}
          onMessage={handleMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          renderLoading={() => (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <ThemedText style={styles.loadingText}>Cargando TradingView...</ThemedText>
            </View>
          )}
        />
      </View>

      {/* Modal de selección de símbolos */}
      <Modal
        visible={showSymbolPicker}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSymbolPicker(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <ThemedText type="title" style={styles.modalTitle}>
              Seleccionar Símbolo
            </ThemedText>
            <TouchableOpacity onPress={() => setShowSymbolPicker(false)}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {Object.entries(popularSymbols).map(([category, symbols]) => (
              <View key={category} style={styles.categorySection}>
                <ThemedText style={styles.categoryTitle}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </ThemedText>
                {symbols.map((item) => (
                  <TouchableOpacity
                    key={item.symbol}
                    style={[
                      styles.symbolItem,
                      { 
                        backgroundColor: currentSymbol === item.symbol 
                          ? colors.primary + '20' 
                          : colors.card 
                      }
                    ]}
                    onPress={() => changeSymbol(item.symbol)}
                  >
                    <View>
                      <ThemedText style={styles.symbolName}>{item.name}</ThemedText>
                      <ThemedText style={[styles.symbolCode, { color: colors.secondary }]}>
                        {item.symbol}
                      </ThemedText>
                    </View>
                    {currentSymbol === item.symbol && (
                      <Ionicons name="checkmark" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </ScrollView>
        </View>
      </Modal>

      {/* Modal de análisis */}
      <Modal
        visible={showAnalysisModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAnalysisModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <ThemedText type="title" style={styles.modalTitle}>
              Análisis GermAyori
            </ThemedText>
            <TouchableOpacity onPress={() => setShowAnalysisModal(false)}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {analysis && (
              <View style={styles.analysisContent}>
                {/* Resultado principal */}
                <View style={[styles.resultCard, { backgroundColor: colors.card }]}>
                  <View style={styles.resultHeader}>
                    <Ionicons
                      name={analysis.isValidEntry ? 'checkmark-circle' : 'close-circle'}
                      size={32}
                      color={analysis.isValidEntry ? '#00FF88' : '#FF4757'}
                    />
                    <View style={styles.resultInfo}>
                      <ThemedText style={styles.resultTitle}>
                        {analysis.isValidEntry ? 'Entrada Válida Detectada' : 'Sin Entrada Clara'}
                      </ThemedText>
                      <ThemedText style={[styles.confidenceText, { color: colors.secondary }]}>
                        Confianza: {analysis.confidence}%
                      </ThemedText>
                    </View>
                  </View>

                  {analysis.entry && (
                    <View style={styles.entryDetails}>
                      <View style={[styles.signalBadge, { backgroundColor: getSignalColor(analysis.entry.type) }]}>
                        <Text style={styles.signalText}>{analysis.entry.type}</Text>
                      </View>
                      
                      <View style={styles.priceGrid}>
                        <View style={styles.priceItem}>
                          <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>Entrada</ThemedText>
                          <ThemedText style={styles.priceValue}>{analysis.entry.price}</ThemedText>
                        </View>
                        <View style={styles.priceItem}>
                          <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>Take Profit</ThemedText>
                          <ThemedText style={[styles.priceValue, { color: '#00FF88' }]}>
                            {analysis.entry.takeProfit}
                          </ThemedText>
                        </View>
                        <View style={styles.priceItem}>
                          <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>Stop Loss</ThemedText>
                          <ThemedText style={[styles.priceValue, { color: '#FF4757' }]}>
                            {analysis.entry.stopLoss}
                          </ThemedText>
                        </View>
                        <View style={styles.priceItem}>
                          <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>Risk/Reward</ThemedText>
                          <ThemedText style={[styles.priceValue, { color: '#FFD700' }]}>
                            1:{analysis.entry.riskReward}
                          </ThemedText>
                        </View>
                      </View>
                    </View>
                  )}
                </View>

                {/* Análisis detallado */}
                <View style={[styles.analysisCard, { backgroundColor: colors.card }]}>
                  <ThemedText style={styles.analysisTitle}>Análisis Detallado</ThemedText>
                  <Text style={[styles.analysisText, { color: colors.text }]}>
                    {formatAnalysisForDisplay(analysis)}
                  </Text>
                </View>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  symbolButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    gap: 4,
  },
  symbolText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  timeframeContainer: {
    flex: 1,
  },
  timeframeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
  },
  timeframeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  analyzeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartContainer: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#FFFFFF',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textTransform: 'capitalize',
  },
  symbolItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  symbolName: {
    fontSize: 16,
    fontWeight: '600',
  },
  symbolCode: {
    fontSize: 12,
    marginTop: 2,
  },
  analysisContent: {
    gap: 16,
  },
  resultCard: {
    borderRadius: 12,
    padding: 20,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 16,
  },
  resultInfo: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  confidenceText: {
    fontSize: 14,
    marginTop: 4,
  },
  entryDetails: {
    gap: 12,
  },
  signalBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  signalText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  priceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  priceItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  analysisCard: {
    borderRadius: 12,
    padding: 20,
  },
  analysisTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  analysisText: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'monospace',
  },
});
