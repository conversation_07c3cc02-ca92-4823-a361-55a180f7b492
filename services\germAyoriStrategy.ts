import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';

// Interfaces para la estrategia GermAyori
export interface GermAyoriAnalysis {
  isValidEntry: boolean;
  analysis: {
    daily: string;
    h4: string;
    h1: string;
    m15: string;
    m5: string;
  };
  entry?: {
    type: 'BUY' | 'SELL';
    price: number;
    takeProfit: number;
    stopLoss: number;
    riskReward: number;
    symbol: string;
  };
  reasoning: string;
  confidence: number;
  timestamp: number;
}

export interface TimeframeAnalysis {
  timeframe: 'D1' | 'H4' | 'H1' | 'M15' | 'M5';
  description: string;
  status: 'confirmed' | 'pending' | 'invalid';
  details: string;
}

// Prompt para OpenAI con la estrategia GermAyori
const GERMAYORI_PROMPT = `
Actúa como un experto en trading institucional. Usa la estrategia GermAyori para analizar un gráfico técnico que el usuario ha subido. El análisis se basa en cinco temporalidades: Diario, H4, H1, M15 y M5. Evalúa si hay una entrada clara y válida, y entrega parámetros precisos.

### Estrategia GermAyori

1. **Diario (D1):**
   - Detectar la **tendencia principal** (alcista o bajista).
   - Confirmar si hay estructura limpia o zonas relevantes de decisión.

2. **H4:**
   - Identificar si hay **liquidez tomada** (barridos, mechas, manipulaciones).
   - Detectar zonas de interés (bloques institucionales o zonas de reacción).

3. **H1:**
   - Confirmar **ruptura de estructura (BOS)**.
   - Validar la dirección del movimiento institucional.

4. **M15:**
   - Buscar **Order Block válido**.
   - Evaluar si hay confluencia con la liquidez o ruptura previa.

5. **M5:**
   - Confirmar si hay un **Fair Value Gap limpio** y sin mitigación.
   - Definir la **zona precisa de entrada**.

### Instrucciones de respuesta:

- Analiza cada temporalidad como se explicó.
- Explica brevemente **por qué** se tomaría la entrada o si no es válida.
- Si hay entrada clara, proporciona:

🔸 Tipo: Compra / Venta
🔸 Entrada: (precio exacto)
🔸 TP: (Take Profit)
🔸 SL: (Stop Loss)
🔸 RR: (Relación riesgo-beneficio estimada)

- Usa términos técnicos y justificación clara.

Ejemplo de respuesta esperada:

✅ Entrada válida detectada.

📊 Diario: Tendencia alcista
🔍 H4: Liquidez tomada en zona clave
📉 H1: BOS confirmado al alza
🏦 M15: Order Block alcista identificado
🕵️‍♂️ M5: FVG limpio en retroceso

📈 Compra en 2315
🎯 TP: 2365
🛡️ SL: 2295
📏 RR: 1:2.5

Si no hay entrada clara, indica:
"No hay entrada clara en este momento. Esperar nueva confirmación."

IMPORTANTE: Responde SIEMPRE en formato JSON con esta estructura:
{
  "isValidEntry": boolean,
  "analysis": {
    "daily": "descripción del análisis diario",
    "h4": "descripción del análisis H4",
    "h1": "descripción del análisis H1", 
    "m15": "descripción del análisis M15",
    "m5": "descripción del análisis M5"
  },
  "entry": {
    "type": "BUY" o "SELL",
    "price": número,
    "takeProfit": número,
    "stopLoss": número,
    "riskReward": número,
    "symbol": "símbolo detectado"
  },
  "reasoning": "explicación completa del por qué",
  "confidence": número entre 0-100
}
`;

// Función para seleccionar imagen
export const selectImage = async (): Promise<string | null> => {
  try {
    // Solicitar permisos
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos requeridos', 'Necesitamos acceso a tu galería para analizar gráficos.');
      return null;
    }

    // Abrir selector de imagen
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
      base64: true,
    });

    if (!result.canceled && result.assets[0]) {
      return result.assets[0].base64 || null;
    }

    return null;
  } catch (error) {
    console.error('Error selecting image:', error);
    Alert.alert('Error', 'No se pudo seleccionar la imagen.');
    return null;
  }
};

// Función para tomar foto
export const takePhoto = async (): Promise<string | null> => {
  try {
    // Solicitar permisos de cámara
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos requeridos', 'Necesitamos acceso a tu cámara para tomar fotos de gráficos.');
      return null;
    }

    // Abrir cámara
    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
      base64: true,
    });

    if (!result.canceled && result.assets[0]) {
      return result.assets[0].base64 || null;
    }

    return null;
  } catch (error) {
    console.error('Error taking photo:', error);
    Alert.alert('Error', 'No se pudo tomar la foto.');
    return null;
  }
};

// Función para analizar imagen con OpenAI (simulada)
export const analyzeChartWithGermAyori = async (imageBase64: string): Promise<GermAyoriAnalysis> => {
  try {
    // Simular llamada a OpenAI Vision API
    // En producción, aquí iría la llamada real a OpenAI
    
    // Simular análisis con diferentes resultados
    const mockAnalyses = [
      {
        isValidEntry: true,
        analysis: {
          daily: "Tendencia alcista confirmada con estructura limpia",
          h4: "Liquidez tomada en zona clave de 43,200. Manipulación institucional detectada",
          h1: "BOS confirmado al alza. Ruptura de estructura válida en 43,150",
          m15: "Order Block alcista identificado en zona 43,100-43,120",
          m5: "FVG limpio en retroceso hacia 43,080. Zona de entrada precisa"
        },
        entry: {
          type: 'BUY' as const,
          price: 43085,
          takeProfit: 43285,
          stopLoss: 42985,
          riskReward: 2.0,
          symbol: 'BTC/USD'
        },
        reasoning: "Confluencia perfecta en todas las temporalidades. Tendencia alcista en D1, liquidez tomada en H4, BOS confirmado en H1, Order Block válido en M15 y FVG limpio en M5.",
        confidence: 87
      },
      {
        isValidEntry: false,
        analysis: {
          daily: "Tendencia lateral sin dirección clara",
          h4: "No hay liquidez tomada reciente",
          h1: "Estructura confusa, sin BOS claro",
          m15: "Order Blocks múltiples sin confluencia",
          m5: "FVG ya mitigado, no válido para entrada"
        },
        entry: undefined,
        reasoning: "No hay confluencia entre temporalidades. Estructura confusa en H1 y FVG ya mitigado en M5. Esperar nueva confirmación.",
        confidence: 25
      },
      {
        isValidEntry: true,
        analysis: {
          daily: "Tendencia bajista dominante",
          h4: "Liquidez tomada en máximos de 2,685. Zona de distribución",
          h1: "BOS bajista confirmado. Estructura de venta institucional",
          m15: "Order Block bajista en 2,670-2,675",
          m5: "FVG bajista limpio en 2,668. Entrada precisa identificada"
        },
        entry: {
          type: 'SELL' as const,
          price: 2668,
          takeProfit: 2618,
          stopLoss: 2688,
          riskReward: 2.5,
          symbol: 'ETH/USD'
        },
        reasoning: "Setup bajista perfecto. Tendencia bajista en D1, liquidez tomada en H4, BOS bajista en H1, Order Block bajista en M15 y FVG bajista limpio en M5.",
        confidence: 92
      }
    ];

    // Simular tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Retornar análisis aleatorio
    const randomAnalysis = mockAnalyses[Math.floor(Math.random() * mockAnalyses.length)];
    
    return {
      ...randomAnalysis,
      timestamp: Date.now()
    };

  } catch (error) {
    console.error('Error analyzing chart:', error);
    throw new Error('Error al analizar el gráfico. Inténtalo de nuevo.');
  }
};

// Función para analizar imagen real con OpenAI (para implementación futura)
export const analyzeChartWithOpenAI = async (imageBase64: string, apiKey: string): Promise<GermAyoriAnalysis> => {
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: GERMAYORI_PROMPT
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const analysisText = data.choices[0].message.content;
    
    // Parsear respuesta JSON
    const analysis = JSON.parse(analysisText);
    
    return {
      ...analysis,
      timestamp: Date.now()
    };

  } catch (error) {
    console.error('Error with OpenAI API:', error);
    // Fallback a análisis simulado
    return analyzeChartWithGermAyori(imageBase64);
  }
};

// Función para formatear el análisis para mostrar
export const formatAnalysisForDisplay = (analysis: GermAyoriAnalysis): string => {
  if (!analysis.isValidEntry) {
    return `❌ No hay entrada clara en este momento.

📊 **Análisis por Temporalidades:**

🗓️ **Diario:** ${analysis.analysis.daily}
🕐 **H4:** ${analysis.analysis.h4}  
🕐 **H1:** ${analysis.analysis.h1}
🕐 **M15:** ${analysis.analysis.m15}
🕐 **M5:** ${analysis.analysis.m5}

💭 **Razón:** ${analysis.reasoning}

⏳ Esperar nueva confirmación.`;
  }

  const entry = analysis.entry!;
  return `✅ **Entrada válida detectada**

📊 **Análisis por Temporalidades:**

🗓️ **Diario:** ${analysis.analysis.daily}
🕐 **H4:** ${analysis.analysis.h4}
🕐 **H1:** ${analysis.analysis.h1}
🕐 **M15:** ${analysis.analysis.m15}
🕐 **M5:** ${analysis.analysis.m5}

📈 **Setup de Trading:**
🔸 **Tipo:** ${entry.type}
🔸 **Entrada:** ${entry.price}
🔸 **TP:** ${entry.takeProfit}
🔸 **SL:** ${entry.stopLoss}
🔸 **RR:** 1:${entry.riskReward}
🔸 **Símbolo:** ${entry.symbol}

💭 **Justificación:** ${analysis.reasoning}

🎯 **Confianza:** ${analysis.confidence}%`;
};

// Función para guardar análisis
export const saveAnalysis = async (analysis: GermAyoriAnalysis, imageBase64: string) => {
  try {
    // Aquí se guardaría en AsyncStorage o base de datos
    const savedAnalysis = {
      ...analysis,
      imageBase64,
      id: Date.now().toString()
    };
    
    // Implementar guardado real
    console.log('Analysis saved:', savedAnalysis);
    
    return savedAnalysis;
  } catch (error) {
    console.error('Error saving analysis:', error);
    throw error;
  }
};
