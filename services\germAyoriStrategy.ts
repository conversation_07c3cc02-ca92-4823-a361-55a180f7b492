import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';

// Interfaces para la estrategia GermAyori
export interface GermAyoriAnalysis {
  isValidEntry: boolean;
  analysis: {
    daily: string;
    h4: string;
    h1: string;
    m15: string;
    m5: string;
  };
  entry?: {
    type: 'BUY' | 'SELL';
    price: number;
    takeProfit: number;
    stopLoss: number;
    riskReward: number;
    symbol: string;
  };
  reasoning: string;
  confidence: number;
  timestamp: number;
}

export interface TimeframeAnalysis {
  timeframe: 'D1' | 'H4' | 'H1' | 'M15' | 'M5';
  description: string;
  status: 'confirmed' | 'pending' | 'invalid';
  details: string;
}

// Prompt para OpenAI con la estrategia GermAyori
const GERMAYORI_PROMPT = `
Actúa como un experto en trading institucional. Usa la estrategia GermAyori para analizar un gráfico técnico que el usuario ha subido. El análisis se basa en cinco temporalidades: Diario, H4, H1, M15 y M5. Evalúa si hay una entrada clara y válida, y entrega parámetros precisos.

### Estrategia GermAyori

1. **Diario (D1):**
   - Detectar la **tendencia principal** (alcista o bajista).
   - Confirmar si hay estructura limpia o zonas relevantes de decisión.

2. **H4:**
   - Identificar si hay **liquidez tomada** (barridos, mechas, manipulaciones).
   - Detectar zonas de interés (bloques institucionales o zonas de reacción).

3. **H1:**
   - Confirmar **ruptura de estructura (BOS)**.
   - Validar la dirección del movimiento institucional.

4. **M15:**
   - Buscar **Order Block válido**.
   - Evaluar si hay confluencia con la liquidez o ruptura previa.

5. **M5:**
   - Confirmar si hay un **Fair Value Gap limpio** y sin mitigación.
   - Definir la **zona precisa de entrada**.

### Instrucciones de respuesta:

- Analiza cada temporalidad como se explicó.
- Explica brevemente **por qué** se tomaría la entrada o si no es válida.
- Si hay entrada clara, proporciona:

🔸 Tipo: Compra / Venta
🔸 Entrada: (precio exacto)
🔸 TP: (Take Profit)
🔸 SL: (Stop Loss)
🔸 RR: (Relación riesgo-beneficio estimada)

- Usa términos técnicos y justificación clara.

Ejemplo de respuesta esperada:

✅ Entrada válida detectada.

📊 Diario: Tendencia alcista
🔍 H4: Liquidez tomada en zona clave
📉 H1: BOS confirmado al alza
🏦 M15: Order Block alcista identificado
🕵️‍♂️ M5: FVG limpio en retroceso

📈 Compra en 2315
🎯 TP: 2365
🛡️ SL: 2295
📏 RR: 1:2.5

Si no hay entrada clara, indica:
"No hay entrada clara en este momento. Esperar nueva confirmación."

IMPORTANTE: Responde SIEMPRE en formato JSON con esta estructura:
{
  "isValidEntry": boolean,
  "analysis": {
    "daily": "descripción del análisis diario",
    "h4": "descripción del análisis H4",
    "h1": "descripción del análisis H1", 
    "m15": "descripción del análisis M15",
    "m5": "descripción del análisis M5"
  },
  "entry": {
    "type": "BUY" o "SELL",
    "price": número,
    "takeProfit": número,
    "stopLoss": número,
    "riskReward": número,
    "symbol": "símbolo detectado"
  },
  "reasoning": "explicación completa del por qué",
  "confidence": número entre 0-100
}
`;

// Función para seleccionar imagen
export const selectImage = async (): Promise<string | null> => {
  try {
    // Solicitar permisos
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos requeridos', 'Necesitamos acceso a tu galería para analizar gráficos.');
      return null;
    }

    // Abrir selector de imagen
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
      base64: true,
    });

    if (!result.canceled && result.assets[0]) {
      return result.assets[0].base64 || null;
    }

    return null;
  } catch (error) {
    console.error('Error selecting image:', error);
    Alert.alert('Error', 'No se pudo seleccionar la imagen.');
    return null;
  }
};

// Función para tomar foto
export const takePhoto = async (): Promise<string | null> => {
  try {
    // Solicitar permisos de cámara
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos requeridos', 'Necesitamos acceso a tu cámara para tomar fotos de gráficos.');
      return null;
    }

    // Abrir cámara
    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
      base64: true,
    });

    if (!result.canceled && result.assets[0]) {
      return result.assets[0].base64 || null;
    }

    return null;
  } catch (error) {
    console.error('Error taking photo:', error);
    Alert.alert('Error', 'No se pudo tomar la foto.');
    return null;
  }
};

// Función para analizar gráfico con estrategia GermAyori - REQUIERE IMPLEMENTACIÓN REAL
export const analyzeChartWithGermAyori = async (imageBase64: string): Promise<GermAyoriAnalysis> => {
  try {
    // ⚠️ ESTA FUNCIÓN NECESITA IMPLEMENTACIÓN REAL CON IA
    // NO ES SIMULACIÓN - Debe conectarse con API real de análisis de gráficos

    // Tiempo de procesamiento real
    // Simular tiempo de procesamiento real
    await new Promise(resolve => setTimeout(resolve, 3000));

    // RETORNAR ANÁLISIS QUE INDICA NECESIDAD DE IMPLEMENTACIÓN REAL
    return {
      isValidEntry: false,
      analysis: {
        daily: "🔧 Análisis real pendiente - Conectar con API de IA",
        h4: "⚠️ Función requiere implementación con análisis real",
        h1: "🤖 Necesita IA para detectar patrones GermAyori",
        m15: "📊 Análisis de Order Blocks pendiente de implementar",
        m5: "🎯 Detección de FVG requiere IA real"
      },
      entry: undefined,
      reasoning: "Esta función es un placeholder. Necesita implementación real con IA para analizar gráficos de TradingView y detectar patrones de la estrategia GermAyori (Fair Value Gaps, Order Blocks, BOS, etc.).",
      confidence: 0,
      timestamp: Date.now()
    };

  } catch (error) {
    console.error('Error analyzing chart:', error);
    throw new Error('Error al analizar el gráfico. Inténtalo de nuevo.');
  }
};

// Función para analizar imagen real con OpenAI (para implementación futura)
export const analyzeChartWithOpenAI = async (imageBase64: string, apiKey: string): Promise<GermAyoriAnalysis> => {
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: GERMAYORI_PROMPT
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const analysisText = data.choices[0].message.content;
    
    // Parsear respuesta JSON
    const analysis = JSON.parse(analysisText);
    
    return {
      ...analysis,
      timestamp: Date.now()
    };

  } catch (error) {
    console.error('Error with OpenAI API:', error);
    // Fallback a análisis simulado
    return analyzeChartWithGermAyori(imageBase64);
  }
};

// Función para formatear el análisis para mostrar
export const formatAnalysisForDisplay = (analysis: GermAyoriAnalysis): string => {
  if (!analysis.isValidEntry) {
    return `❌ No hay entrada clara en este momento.

📊 **Análisis por Temporalidades:**

🗓️ **Diario:** ${analysis.analysis.daily}
🕐 **H4:** ${analysis.analysis.h4}  
🕐 **H1:** ${analysis.analysis.h1}
🕐 **M15:** ${analysis.analysis.m15}
🕐 **M5:** ${analysis.analysis.m5}

💭 **Razón:** ${analysis.reasoning}

⏳ Esperar nueva confirmación.`;
  }

  const entry = analysis.entry!;
  return `✅ **Entrada válida detectada**

📊 **Análisis por Temporalidades:**

🗓️ **Diario:** ${analysis.analysis.daily}
🕐 **H4:** ${analysis.analysis.h4}
🕐 **H1:** ${analysis.analysis.h1}
🕐 **M15:** ${analysis.analysis.m15}
🕐 **M5:** ${analysis.analysis.m5}

📈 **Setup de Trading:**
🔸 **Tipo:** ${entry.type}
🔸 **Entrada:** ${entry.price}
🔸 **TP:** ${entry.takeProfit}
🔸 **SL:** ${entry.stopLoss}
🔸 **RR:** 1:${entry.riskReward}
🔸 **Símbolo:** ${entry.symbol}

💭 **Justificación:** ${analysis.reasoning}

🎯 **Confianza:** ${analysis.confidence}%`;
};

// Función para guardar análisis
export const saveAnalysis = async (analysis: GermAyoriAnalysis, imageBase64: string) => {
  try {
    // Aquí se guardaría en AsyncStorage o base de datos
    const savedAnalysis = {
      ...analysis,
      imageBase64,
      id: Date.now().toString()
    };
    
    // Implementar guardado real
    console.log('Analysis saved:', savedAnalysis);
    
    return savedAnalysis;
  } catch (error) {
    console.error('Error saving analysis:', error);
    throw error;
  }
};
