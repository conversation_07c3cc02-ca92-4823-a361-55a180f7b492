import axios from 'axios';

// APIs REALES para datos en tiempo real - TRADINGVIEW
const TRADINGVIEW_API = 'https://scanner.tradingview.com';
const YAHOO_FINANCE_API = 'https://query1.finance.yahoo.com/v8/finance/chart';
const BINANCE_API = 'https://api.binance.com/api/v3';
const FINNHUB_API = 'https://finnhub.io/api/v1';

// API Keys
const FINNHUB_API_KEY = 'ctqnqj9r01qvbqnqhqd0ctqnqj9r01qvbqnqhqdg';

export interface RealTimePrice {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  changePercent24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  market: string;
  lastUpdate: number;
}

export interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MarketData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  category: 'crypto' | 'forex' | 'stocks' | 'commodities' | 'futures';
}

// TRADINGVIEW SCANNER - DATOS REALES
export const getTradingViewData = async (market: string = 'crypto'): Promise<MarketData[]> => {
  try {
    const filters = {
      crypto: {
        markets: ['crypto'],
        symbols: ['BINANCE:BTCUSDT', 'BINANCE:ETHUSDT', 'BINANCE:ADAUSDT', 'BINANCE:SOLUSDT', 'BINANCE:DOTUSDT']
      },
      forex: {
        markets: ['forex'],
        symbols: ['FX:EURUSD', 'FX:GBPUSD', 'FX:USDJPY', 'FX:AUDUSD', 'FX:USDCAD']
      },
      stocks: {
        markets: ['america'],
        symbols: ['NASDAQ:AAPL', 'NASDAQ:GOOGL', 'NASDAQ:MSFT', 'NASDAQ:TSLA', 'NYSE:JPM']
      },
      commodities: {
        markets: ['futures'],
        symbols: ['COMEX:GC1!', 'NYMEX:CL1!', 'COMEX:SI1!', 'CBOT:ZW1!', 'NYMEX:NG1!']
      }
    };

    const marketConfig = filters[market as keyof typeof filters] || filters.crypto;
    
    const payload = {
      filter: [
        { left: 'exchange', operation: 'in_range', right: marketConfig.markets }
      ],
      options: {
        lang: 'es'
      },
      symbols: {
        query: {
          types: []
        },
        tickers: marketConfig.symbols
      },
      columns: [
        'name', 'close', 'change', 'change_abs', 'volume', 'market_cap_basic'
      ],
      sort: {
        sortBy: 'volume',
        sortOrder: 'desc'
      },
      range: [0, 50]
    };

    const response = await axios.post(`${TRADINGVIEW_API}/${market}/scan`, payload, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    return response.data.data.map((item: any) => ({
      symbol: item.s,
      name: item.d[0] || item.s,
      price: item.d[1] || 0,
      change: item.d[3] || 0,
      changePercent: item.d[2] || 0,
      volume: item.d[4] || 0,
      marketCap: item.d[5] || 0,
      category: market as any
    }));

  } catch (error) {
    console.error('Error fetching TradingView data:', error);
    return getFallbackData(market);
  }
};

// BINANCE API - CRYPTO REAL TIME
export const getRealTimePrice = async (symbol: string = 'BTCUSDT'): Promise<RealTimePrice> => {
  try {
    const [ticker24h, price] = await Promise.all([
      axios.get(`${BINANCE_API}/ticker/24hr?symbol=${symbol}`),
      axios.get(`${BINANCE_API}/ticker/price?symbol=${symbol}`)
    ]);

    const data = ticker24h.data;
    
    return {
      symbol: symbol.replace('USDT', '/USD'),
      name: symbol.replace('USDT', ''),
      price: parseFloat(price.data.price),
      change24h: parseFloat(data.priceChange),
      changePercent24h: parseFloat(data.priceChangePercent),
      volume24h: parseFloat(data.volume),
      high24h: parseFloat(data.highPrice),
      low24h: parseFloat(data.lowPrice),
      market: 'crypto',
      lastUpdate: Date.now()
    };
  } catch (error) {
    console.error('Error fetching real-time price:', error);
    throw error;
  }
};

// YAHOO FINANCE - STOCKS & FOREX
export const getYahooFinanceData = async (symbol: string): Promise<RealTimePrice> => {
  try {
    const response = await axios.get(`${YAHOO_FINANCE_API}/${symbol}`, {
      params: {
        interval: '1d',
        range: '2d'
      }
    });

    const result = response.data.chart.result[0];
    const meta = result.meta;
    const quote = result.indicators.quote[0];
    
    const currentPrice = meta.regularMarketPrice;
    const previousClose = meta.previousClose;
    const change = currentPrice - previousClose;
    const changePercent = (change / previousClose) * 100;

    return {
      symbol: meta.symbol,
      name: meta.longName || meta.symbol,
      price: currentPrice,
      change24h: change,
      changePercent24h: changePercent,
      volume24h: meta.regularMarketVolume || 0,
      high24h: meta.regularMarketDayHigh || currentPrice,
      low24h: meta.regularMarketDayLow || currentPrice,
      market: symbol.includes('=X') ? 'forex' : 'stocks',
      lastUpdate: Date.now()
    };
  } catch (error) {
    console.error('Error fetching Yahoo Finance data:', error);
    throw error;
  }
};

// CANDLESTICK DATA
export const getCandlestickData = async (
  symbol: string = 'BTCUSDT',
  interval: string = '1h',
  limit: number = 100
): Promise<CandleData[]> => {
  try {
    const response = await axios.get(`${BINANCE_API}/klines`, {
      params: {
        symbol,
        interval,
        limit
      }
    });

    return response.data.map((candle: any[]) => ({
      timestamp: candle[0],
      open: parseFloat(candle[1]),
      high: parseFloat(candle[2]),
      low: parseFloat(candle[3]),
      close: parseFloat(candle[4]),
      volume: parseFloat(candle[5])
    }));
  } catch (error) {
    console.error('Error fetching candlestick data:', error);
    return [];
  }
};

// FALLBACK DATA
const getFallbackData = (market: string): MarketData[] => {
  const fallbackData = {
    crypto: [
      { symbol: 'BTC/USD', name: 'Bitcoin', price: 43250, change: 1250, changePercent: 2.98, volume: 28500000000, category: 'crypto' as const },
      { symbol: 'ETH/USD', name: 'Ethereum', price: 2650, change: -45, changePercent: -1.67, volume: 15200000000, category: 'crypto' as const },
      { symbol: 'ADA/USD', name: 'Cardano', price: 0.485, change: 0.025, changePercent: 5.43, volume: 890000000, category: 'crypto' as const }
    ],
    forex: [
      { symbol: 'EUR/USD', name: 'Euro/Dólar', price: 1.0875, change: 0.0025, changePercent: 0.23, volume: 0, category: 'forex' as const },
      { symbol: 'GBP/USD', name: 'Libra/Dólar', price: 1.2650, change: -0.0045, changePercent: -0.35, volume: 0, category: 'forex' as const }
    ],
    stocks: [
      { symbol: 'AAPL', name: 'Apple Inc.', price: 185.25, change: 2.15, changePercent: 1.17, volume: 45000000, category: 'stocks' as const },
      { symbol: 'GOOGL', name: 'Alphabet Inc.', price: 142.50, change: -1.25, changePercent: -0.87, volume: 28000000, category: 'stocks' as const }
    ],
    commodities: [
      { symbol: 'GOLD', name: 'Oro', price: 2025.50, change: 15.25, changePercent: 0.76, volume: 0, category: 'commodities' as const },
      { symbol: 'OIL', name: 'Petróleo WTI', price: 78.45, change: -1.25, changePercent: -1.57, volume: 0, category: 'commodities' as const }
    ]
  };

  return fallbackData[market as keyof typeof fallbackData] || fallbackData.crypto;
};

// FUNCIÓN PRINCIPAL PARA OBTENER DATOS DE MERCADO
export const getMarketData = async (category: string = 'crypto'): Promise<MarketData[]> => {
  try {
    // Primero intentar TradingView
    const tradingViewData = await getTradingViewData(category);
    if (tradingViewData.length > 0) {
      return tradingViewData;
    }

    // Si TradingView falla, usar APIs específicas
    if (category === 'crypto') {
      const btcData = await getRealTimePrice('BTCUSDT');
      const ethData = await getRealTimePrice('ETHUSDT');
      return [
        {
          symbol: btcData.symbol,
          name: btcData.name,
          price: btcData.price,
          change: btcData.change24h,
          changePercent: btcData.changePercent24h,
          volume: btcData.volume24h,
          category: 'crypto'
        },
        {
          symbol: ethData.symbol,
          name: ethData.name,
          price: ethData.price,
          change: ethData.change24h,
          changePercent: ethData.changePercent24h,
          volume: ethData.volume24h,
          category: 'crypto'
        }
      ];
    }

    // Fallback para otros mercados
    return getFallbackData(category);
  } catch (error) {
    console.error('Error in getMarketData:', error);
    return getFallbackData(category);
  }
};

// WEBSOCKET PARA DATOS EN TIEMPO REAL
export const subscribeToRealTimeData = (symbol: string, callback: (data: RealTimePrice) => void) => {
  const ws = new WebSocket(`wss://stream.binance.com:9443/ws/${symbol.toLowerCase()}@ticker`);
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    const realTimeData: RealTimePrice = {
      symbol: data.s.replace('USDT', '/USD'),
      name: data.s.replace('USDT', ''),
      price: parseFloat(data.c),
      change24h: parseFloat(data.P),
      changePercent24h: parseFloat(data.P),
      volume24h: parseFloat(data.v),
      high24h: parseFloat(data.h),
      low24h: parseFloat(data.l),
      market: 'crypto',
      lastUpdate: Date.now()
    };
    callback(realTimeData);
  };

  return ws;
};
