# 📊 Pinelo AI - Trading Intelligence App

Una aplicación móvil profesional de análisis financiero y trading desarrollada con React Native y Expo, inspirada en las mejores plataformas de trading del mercado.

## 🚀 Características Principales

### 📈 Dashboard de Trading
- **Gráficos de velas (candlestick)** interactivos en tiempo real
- **Métricas financieras** completas (precio, volumen, cambios 24h)
- **Múltiples timeframes** (15M, 1H, 4H, 1D, 1W)
- **Indicadores técnicos** integrados

### 🤖 Análisis AI
- **Indicador de análisis** con animaciones fluidas
- **Señales de trading** (Comprar/Vender/Mantener)
- **Nivel de confianza** del análisis
- **Múltiples tipos de análisis** (técnico, sentimiento, correlación)

### 💼 Gestión de Portfolio
- **Balance total** con cambios en tiempo real
- **Posiciones activas** con P&L
- **Múltiples criptomonedas** soportadas

### ⚙️ Configuración Avanzada
- **Modo oscuro** optimizado para trading
- **Notificaciones** personalizables
- **Feedback háptico** para mejor UX

## 🛠️ Tecnologías Utilizadas

- **React Native 0.79.2** - Framework principal
- **Expo SDK 53** - Plataforma de desarrollo
- **TypeScript** - Tipado estático
- **React Native SVG** - Gráficos vectoriales
- **React Native Reanimated** - Animaciones fluidas
- **Expo Haptics** - Retroalimentación táctil

## 📱 Instalación y Configuración

1. **Instalar dependencias**
   ```bash
   npm install
   ```

2. **Iniciar la aplicación**
   ```bash
   npm start
   ```

3. **Opciones de visualización:**
   - **Web**: Presiona `w` o visita `http://localhost:8082`
   - **Android**: Presiona `a` o escanea el QR con Expo Go
   - **iOS**: Presiona `i` o escanea el QR con la cámara

## 🎨 Estructura del Proyecto

```
app/
├── (tabs)/
│   ├── index.tsx          # Dashboard principal
│   ├── explore.tsx        # Análisis AI
│   ├── portfolio.tsx      # Gestión de portfolio
│   └── settings.tsx       # Configuración
├── _layout.tsx            # Layout principal
└── +not-found.tsx         # Página 404

components/
├── trading/
│   ├── CandlestickChart.tsx    # Gráfico de velas
│   ├── TradingMetrics.tsx      # Métricas financieras
│   └── AIAnalysisIndicator.tsx # Indicador de análisis AI
└── ui/                    # Componentes de UI

constants/
└── Colors.ts              # Paleta de colores para trading
```

## 🎯 Características de la Interfaz

### Colores Profesionales
- **Verde (#00C851)**: Tendencias alcistas
- **Rojo (#FF4444)**: Tendencias bajistas
- **Azul (#00D4AA)**: Elementos de acento
- **Fondo oscuro (#1A1D29)**: Optimizado para trading

### Animaciones Fluidas
- **Indicador de análisis** con rotación y pulso
- **Transiciones suaves** entre pantallas
- **Feedback háptico** en interacciones

## 🔧 Scripts Disponibles

- `npm start` - Inicia el servidor de desarrollo
- `npm run android` - Abre en Android
- `npm run ios` - Abre en iOS
- `npm run web` - Abre en navegador web
- `npm run lint` - Ejecuta el linter

## 📊 Datos de Ejemplo

La aplicación incluye datos simulados para demostración:
- Precios de Bitcoin en tiempo real simulado
- Análisis AI con diferentes señales
- Portfolio con múltiples criptomonedas

## 🚀 Próximas Características

- [ ] Integración con APIs reales de trading
- [ ] Más indicadores técnicos (RSI, MACD, Bollinger Bands)
- [ ] Notificaciones push para señales
- [ ] Modo de trading en papel
- [ ] Análisis de noticias en tiempo real

## 📄 Licencia

Este proyecto está bajo la licencia MIT.
