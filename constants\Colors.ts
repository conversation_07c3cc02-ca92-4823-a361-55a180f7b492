/**
 * Colores para la aplicación de análisis financiero Pinelo AI
 * Diseño profesional para trading y análisis de mercados
 */

const tintColorLight = '#00D4AA';
const tintColorDark = '#00D4AA';

// Colores para gráficos financieros
export const TradingColors = {
  bullish: '#00C851', // Verde para tendencia alcista
  bearish: '#FF4444', // Rojo para tendencia bajista
  neutral: '#FFB74D', // Amarillo para neutral
  volume: '#6C7B7F', // Gris para volumen
  grid: '#2E3440', // Líneas de grid
  background: '#1A1D29', // Fondo principal
  cardBackground: '#252A3A', // Fondo de tarjetas
  accent: '#00D4AA', // Color de acento
  warning: '#FFA726',
  info: '#42A5F5',
};

export const Colors = {
  light: {
    text: '#11181C',
    background: '#FFFFFF',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    card: '#F8F9FA',
    border: '#E1E5E9',
    primary: '#00D4AA',
    secondary: '#6C7B7F',
    success: '#00C851',
    danger: '#FF4444',
    warning: '#FFA726',
    info: '#42A5F5',
  },
  dark: {
    text: '#FFFFFF',
    background: '#1A1D29',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    card: '#252A3A',
    border: '#2E3440',
    primary: '#00D4AA',
    secondary: '#6C7B7F',
    success: '#00C851',
    danger: '#FF4444',
    warning: '#FFA726',
    info: '#42A5F5',
  },
};
