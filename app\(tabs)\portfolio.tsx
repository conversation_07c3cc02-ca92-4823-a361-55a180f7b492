import React from 'react';
import { StyleSheet, ScrollView, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors, TradingColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function PortfolioScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Portfolio</ThemedText>
          <ThemedText style={styles.subtitle}>Gestiona tus inversiones</ThemedText>
        </ThemedView>

        <ThemedView style={[styles.card, { backgroundColor: colors.card }]}>
          <ThemedText type="subtitle" style={styles.cardTitle}>Balance Total</ThemedText>
          <ThemedText style={[styles.balance, { color: TradingColors.accent }]}>
            $25,847.32
          </ThemedText>
          <ThemedText style={[styles.change, { color: TradingColors.bullish }]}>
            +$1,234.56 (+5.02%) hoy
          </ThemedText>
        </ThemedView>

        <ThemedView style={[styles.card, { backgroundColor: colors.card }]}>
          <ThemedText type="subtitle" style={styles.cardTitle}>Posiciones Activas</ThemedText>
          
          <View style={styles.positionItem}>
            <View style={styles.positionHeader}>
              <ThemedText style={styles.symbol}>BTC/USD</ThemedText>
              <ThemedText style={[styles.pnl, { color: TradingColors.bullish }]}>+12.5%</ThemedText>
            </View>
            <ThemedText style={styles.positionDetails}>
              Cantidad: 0.5 BTC • Precio: $42,500
            </ThemedText>
          </View>

          <View style={styles.positionItem}>
            <View style={styles.positionHeader}>
              <ThemedText style={styles.symbol}>ETH/USD</ThemedText>
              <ThemedText style={[styles.pnl, { color: TradingColors.bearish }]}>-3.2%</ThemedText>
            </View>
            <ThemedText style={styles.positionDetails}>
              Cantidad: 5.2 ETH • Precio: $2,850
            </ThemedText>
          </View>

          <View style={styles.positionItem}>
            <View style={styles.positionHeader}>
              <ThemedText style={styles.symbol}>ADA/USD</ThemedText>
              <ThemedText style={[styles.pnl, { color: TradingColors.bullish }]}>+8.7%</ThemedText>
            </View>
            <ThemedText style={styles.positionDetails}>
              Cantidad: 1,500 ADA • Precio: $0.45
            </ThemedText>
          </View>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  card: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  balance: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  change: {
    fontSize: 16,
    fontWeight: '500',
  },
  positionItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  positionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  symbol: {
    fontSize: 16,
    fontWeight: '600',
  },
  pnl: {
    fontSize: 16,
    fontWeight: '600',
  },
  positionDetails: {
    fontSize: 14,
    opacity: 0.7,
  },
});
