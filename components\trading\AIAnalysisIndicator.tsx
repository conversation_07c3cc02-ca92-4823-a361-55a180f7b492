import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { TradingColors, Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface AIAnalysisIndicatorProps {
  isAnalyzing: boolean;
  confidence?: number;
  signal?: 'buy' | 'sell' | 'hold';
  onAnalysisComplete?: () => void;
}

export function AIAnalysisIndicator({ 
  isAnalyzing, 
  confidence = 0, 
  signal,
  onAnalysisComplete 
}: AIAnalysisIndicatorProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isAnalyzing) {
      // Animación de rotación
      const rotateAnimation = Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );

      // Animación de pulso
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );

      // Animación de progreso
      const progressAnimation = Animated.timing(progressAnim, {
        toValue: 1,
        duration: 3000,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      });

      rotateAnimation.start();
      pulseAnimation.start();
      progressAnimation.start(() => {
        if (onAnalysisComplete) {
          onAnalysisComplete();
        }
      });

      return () => {
        rotateAnimation.stop();
        pulseAnimation.stop();
        progressAnimation.stop();
      };
    } else {
      rotateAnim.setValue(0);
      pulseAnim.setValue(1);
      progressAnim.setValue(0);
    }
  }, [isAnalyzing]);

  const getSignalColor = () => {
    switch (signal) {
      case 'buy':
        return TradingColors.bullish;
      case 'sell':
        return TradingColors.bearish;
      case 'hold':
        return TradingColors.neutral;
      default:
        return TradingColors.accent;
    }
  };

  const getSignalText = () => {
    switch (signal) {
      case 'buy':
        return 'COMPRAR';
      case 'sell':
        return 'VENDER';
      case 'hold':
        return 'MANTENER';
      default:
        return 'ANALIZANDO...';
    }
  };

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <ThemedView style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        <ThemedText style={styles.title}>Análisis AI</ThemedText>
        <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
          Pinelo Intelligence
        </ThemedText>
      </View>

      <View style={styles.analysisContainer}>
        {isAnalyzing ? (
          <>
            <Animated.View 
              style={[
                styles.loadingIndicator,
                {
                  transform: [
                    { rotate: spin },
                    { scale: pulseAnim }
                  ]
                }
              ]}
            >
              <View style={[styles.loadingRing, { borderColor: TradingColors.accent }]} />
              <View style={[styles.loadingCore, { backgroundColor: TradingColors.accent }]} />
            </Animated.View>
            
            <ThemedText style={styles.analysisText}>
              Analizando patrones de mercado...
            </ThemedText>
            
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                <Animated.View 
                  style={[
                    styles.progressFill,
                    { 
                      backgroundColor: TradingColors.accent,
                      width: progressWidth
                    }
                  ]} 
                />
              </View>
            </View>
          </>
        ) : (
          <>
            <View style={[styles.signalIndicator, { backgroundColor: getSignalColor() }]}>
              <ThemedText style={styles.signalText}>
                {getSignalText()}
              </ThemedText>
            </View>
            
            {confidence > 0 && (
              <View style={styles.confidenceContainer}>
                <ThemedText style={[styles.confidenceLabel, { color: colors.secondary }]}>
                  Confianza
                </ThemedText>
                <ThemedText style={[styles.confidenceValue, { color: getSignalColor() }]}>
                  {(confidence * 100).toFixed(1)}%
                </ThemedText>
              </View>
            )}
          </>
        )}
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  analysisContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingIndicator: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  loadingRing: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderTopColor: 'transparent',
  },
  loadingCore: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  analysisText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  progressContainer: {
    width: '100%',
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  signalIndicator: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    marginBottom: 16,
  },
  signalText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  confidenceLabel: {
    fontSize: 14,
  },
  confidenceValue: {
    fontSize: 16,
    fontWeight: '600',
  },
});
