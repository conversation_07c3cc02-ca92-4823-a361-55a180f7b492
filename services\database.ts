import mongoose from 'mongoose';

// Configuración de MongoDB
const MONGODB_URI = 'mongodb+srv://germayori:<EMAIL>/la-legendaria-germayori?retryWrites=true&w=majority';

// Esquema de Usuario
const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  nombreCompleto: {
    type: String,
    required: true,
  },
  edad: {
    type: Number,
    required: true,
    min: 18,
  },
  pais: {
    type: String,
    required: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  suscripcionActiva: {
    type: Boolean,
    default: false,
  },
  fechaSuscripcion: {
    type: Date,
    default: null,
  },
  fechaVencimiento: {
    type: Date,
    default: null,
  },
  metodoPago: {
    type: String,
    enum: ['yappy', 'tarjeta', 'transferencia'],
    default: null,
  },
  transaccionId: {
    type: String,
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  lastLogin: {
    type: Date,
    default: Date.now,
  },
  configuraciones: {
    notificaciones: {
      type: Boolean,
      default: true,
    },
    modoOscuro: {
      type: Boolean,
      default: true,
    },
    feedbackHaptico: {
      type: Boolean,
      default: true,
    },
  },
});

// Esquema de Análisis
const AnalysisSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  symbol: {
    type: String,
    required: true,
  },
  timeframe: {
    type: String,
    required: true,
  },
  signal: {
    type: String,
    enum: ['buy', 'sell', 'hold'],
    required: true,
  },
  confidence: {
    type: Number,
    min: 0,
    max: 1,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  indicators: {
    rsi: Number,
    macd: Number,
    bollinger: {
      upper: Number,
      middle: Number,
      lower: Number,
    },
    volume: Number,
    support: Number,
    resistance: Number,
  },
  strategy: {
    type: String,
    default: 'Legendaria Germayori',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Esquema de Pagos
const PaymentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    default: 'USD',
  },
  method: {
    type: String,
    enum: ['yappy', 'tarjeta', 'transferencia'],
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending',
  },
  transactionId: {
    type: String,
    required: true,
    unique: true,
  },
  yapyReference: {
    type: String,
    default: null,
  },
  description: {
    type: String,
    default: 'Suscripción La Legendaria Germayori - 30 días',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: {
    type: Date,
    default: null,
  },
});

// Crear modelos
export const User = mongoose.models.User || mongoose.model('User', UserSchema);
export const Analysis = mongoose.models.Analysis || mongoose.model('Analysis', AnalysisSchema);
export const Payment = mongoose.models.Payment || mongoose.model('Payment', PaymentSchema);

// Función para conectar a MongoDB
export const connectDB = async () => {
  try {
    if (mongoose.connections[0].readyState) {
      return;
    }
    
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Conectado a MongoDB');
  } catch (error) {
    console.error('❌ Error conectando a MongoDB:', error);
    throw error;
  }
};

// Función para desconectar
export const disconnectDB = async () => {
  try {
    await mongoose.disconnect();
    console.log('🔌 Desconectado de MongoDB');
  } catch (error) {
    console.error('❌ Error desconectando de MongoDB:', error);
  }
};
