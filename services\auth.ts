import AsyncStorage from '@react-native-async-storage/async-storage';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { connectDB, User } from './database';

const JWT_SECRET = 'la-legendaria-germayori-secret-key-2024';

export interface RegisterData {
  email: string;
  nombreCompleto: string;
  edad: number;
  pais: string;
  password: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface UserProfile {
  id: string;
  email: string;
  nombreCompleto: string;
  edad: number;
  pais: string;
  suscripcionActiva: boolean;
  fechaSuscripcion?: Date;
  fechaVencimiento?: Date;
  configuraciones: {
    notificaciones: boolean;
    modoOscuro: boolean;
    feedbackHaptico: boolean;
  };
}

// Registrar nuevo usuario
export const registerUser = async (userData: RegisterData): Promise<{ success: boolean; message: string; user?: UserProfile }> => {
  try {
    await connectDB();

    // Verificar si el usuario ya existe
    const existingUser = await User.findOne({ email: userData.email.toLowerCase() });
    if (existingUser) {
      return {
        success: false,
        message: 'Este email ya está registrado',
      };
    }

    // Validaciones
    if (userData.edad < 18) {
      return {
        success: false,
        message: 'Debes ser mayor de 18 años para registrarte',
      };
    }

    if (userData.password.length < 6) {
      return {
        success: false,
        message: 'La contraseña debe tener al menos 6 caracteres',
      };
    }

    // Encriptar contraseña
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

    // Crear usuario
    const newUser = new User({
      email: userData.email.toLowerCase(),
      nombreCompleto: userData.nombreCompleto,
      edad: userData.edad,
      pais: userData.pais,
      password: hashedPassword,
    });

    await newUser.save();

    // Crear perfil de respuesta (sin contraseña)
    const userProfile: UserProfile = {
      id: newUser._id.toString(),
      email: newUser.email,
      nombreCompleto: newUser.nombreCompleto,
      edad: newUser.edad,
      pais: newUser.pais,
      suscripcionActiva: newUser.suscripcionActiva,
      fechaSuscripcion: newUser.fechaSuscripcion,
      fechaVencimiento: newUser.fechaVencimiento,
      configuraciones: newUser.configuraciones,
    };

    return {
      success: true,
      message: 'Usuario registrado exitosamente',
      user: userProfile,
    };
  } catch (error) {
    console.error('Error en registro:', error);
    return {
      success: false,
      message: 'Error interno del servidor',
    };
  }
};

// Iniciar sesión
export const loginUser = async (loginData: LoginData): Promise<{ success: boolean; message: string; user?: UserProfile; token?: string }> => {
  try {
    await connectDB();

    // Buscar usuario
    const user = await User.findOne({ email: loginData.email.toLowerCase() });
    if (!user) {
      return {
        success: false,
        message: 'Email o contraseña incorrectos',
      };
    }

    // Verificar contraseña
    const isPasswordValid = await bcrypt.compare(loginData.password, user.password);
    if (!isPasswordValid) {
      return {
        success: false,
        message: 'Email o contraseña incorrectos',
      };
    }

    // Actualizar último login
    user.lastLogin = new Date();
    await user.save();

    // Generar token JWT
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Crear perfil de respuesta
    const userProfile: UserProfile = {
      id: user._id.toString(),
      email: user.email,
      nombreCompleto: user.nombreCompleto,
      edad: user.edad,
      pais: user.pais,
      suscripcionActiva: user.suscripcionActiva,
      fechaSuscripcion: user.fechaSuscripcion,
      fechaVencimiento: user.fechaVencimiento,
      configuraciones: user.configuraciones,
    };

    // Guardar token en AsyncStorage
    await AsyncStorage.setItem('userToken', token);
    await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));

    return {
      success: true,
      message: 'Inicio de sesión exitoso',
      user: userProfile,
      token,
    };
  } catch (error) {
    console.error('Error en login:', error);
    return {
      success: false,
      message: 'Error interno del servidor',
    };
  }
};

// Verificar token
export const verifyToken = async (token: string): Promise<{ valid: boolean; userId?: string }> => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return {
      valid: true,
      userId: decoded.userId,
    };
  } catch (error) {
    return {
      valid: false,
    };
  }
};

// Obtener perfil del usuario
export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    await connectDB();
    const user = await User.findById(userId);
    
    if (!user) return null;

    return {
      id: user._id.toString(),
      email: user.email,
      nombreCompleto: user.nombreCompleto,
      edad: user.edad,
      pais: user.pais,
      suscripcionActiva: user.suscripcionActiva,
      fechaSuscripcion: user.fechaSuscripcion,
      fechaVencimiento: user.fechaVencimiento,
      configuraciones: user.configuraciones,
    };
  } catch (error) {
    console.error('Error obteniendo perfil:', error);
    return null;
  }
};

// Cerrar sesión
export const logoutUser = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem('userToken');
    await AsyncStorage.removeItem('userProfile');
  } catch (error) {
    console.error('Error en logout:', error);
  }
};

// Verificar si el usuario está autenticado
export const isAuthenticated = async (): Promise<{ authenticated: boolean; user?: UserProfile }> => {
  try {
    const token = await AsyncStorage.getItem('userToken');
    const userProfileString = await AsyncStorage.getItem('userProfile');

    if (!token || !userProfileString) {
      return { authenticated: false };
    }

    const tokenVerification = await verifyToken(token);
    if (!tokenVerification.valid) {
      await logoutUser();
      return { authenticated: false };
    }

    const userProfile = JSON.parse(userProfileString);
    return {
      authenticated: true,
      user: userProfile,
    };
  } catch (error) {
    console.error('Error verificando autenticación:', error);
    return { authenticated: false };
  }
};

// Actualizar configuraciones del usuario
export const updateUserSettings = async (
  userId: string,
  configuraciones: Partial<UserProfile['configuraciones']>
): Promise<{ success: boolean; message: string }> => {
  try {
    await connectDB();
    
    await User.findByIdAndUpdate(userId, {
      $set: { configuraciones },
    });

    // Actualizar AsyncStorage
    const userProfileString = await AsyncStorage.getItem('userProfile');
    if (userProfileString) {
      const userProfile = JSON.parse(userProfileString);
      userProfile.configuraciones = { ...userProfile.configuraciones, ...configuraciones };
      await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
    }

    return {
      success: true,
      message: 'Configuraciones actualizadas',
    };
  } catch (error) {
    console.error('Error actualizando configuraciones:', error);
    return {
      success: false,
      message: 'Error actualizando configuraciones',
    };
  }
};
