import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { TradingColors, Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface MetricItemProps {
  label: string;
  value: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
}

interface TradingMetricsProps {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high24h: number;
  low24h: number;
  marketCap?: number;
}

const MetricItem: React.FC<MetricItemProps> = ({ label, value, change, changeType }) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return TradingColors.bullish;
      case 'negative':
        return TradingColors.bearish;
      default:
        return colors.text;
    }
  };

  return (
    <View style={styles.metricItem}>
      <ThemedText style={[styles.metricLabel, { color: colors.secondary }]}>
        {label}
      </ThemedText>
      <ThemedText style={styles.metricValue}>
        {value}
      </ThemedText>
      {change && (
        <ThemedText style={[styles.metricChange, { color: getChangeColor() }]}>
          {change}
        </ThemedText>
      )}
    </View>
  );
};

export function TradingMetrics({
  symbol,
  price,
  change,
  changePercent,
  volume,
  high24h,
  low24h,
  marketCap
}: TradingMetricsProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  
  const formatPrice = (value: number) => {
    if (value >= 1) {
      return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    return `$${value.toFixed(6)}`;
  };

  const formatVolume = (value: number) => {
    if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(2)}B`;
    }
    if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(2)}M`;
    }
    if (value >= 1e3) {
      return `$${(value / 1e3).toFixed(2)}K`;
    }
    return `$${value.toFixed(2)}`;
  };

  const formatMarketCap = (value?: number) => {
    if (!value) return 'N/A';
    if (value >= 1e12) {
      return `$${(value / 1e12).toFixed(2)}T`;
    }
    if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(2)}B`;
    }
    if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(2)}M`;
    }
    return `$${value.toLocaleString()}`;
  };

  const changeType = change >= 0 ? 'positive' : 'negative';
  const changeText = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`;

  return (
    <ThemedView style={[styles.container, { backgroundColor: colors.card }]}>
      {/* Header con símbolo y precio principal */}
      <View style={styles.header}>
        <View style={styles.symbolContainer}>
          <ThemedText style={styles.symbol}>{symbol}</ThemedText>
          <ThemedText style={[styles.price, { color: TradingColors.accent }]}>
            {formatPrice(price)}
          </ThemedText>
        </View>
        <View style={styles.changeContainer}>
          <ThemedText style={[styles.change, { color: changeType === 'positive' ? TradingColors.bullish : TradingColors.bearish }]}>
            {changeText}
          </ThemedText>
        </View>
      </View>

      {/* Métricas en grid */}
      <View style={styles.metricsGrid}>
        <MetricItem
          label="Volumen 24h"
          value={formatVolume(volume)}
        />
        <MetricItem
          label="Máximo 24h"
          value={formatPrice(high24h)}
        />
        <MetricItem
          label="Mínimo 24h"
          value={formatPrice(low24h)}
        />
        {marketCap && (
          <MetricItem
            label="Cap. Mercado"
            value={formatMarketCap(marketCap)}
          />
        )}
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  header: {
    marginBottom: 20,
  },
  symbolContainer: {
    marginBottom: 8,
  },
  symbol: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  price: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  changeContainer: {
    alignItems: 'flex-start',
  },
  change: {
    fontSize: 16,
    fontWeight: '600',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    marginBottom: 16,
  },
  metricLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  metricChange: {
    fontSize: 14,
    marginTop: 2,
  },
});
