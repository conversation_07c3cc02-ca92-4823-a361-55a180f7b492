import axios from 'axios';

// APIs gratuitas para datos en tiempo real
const BINANCE_API = 'https://api.binance.com/api/v3';
const COINGECKO_API = 'https://api.coingecko.com/api/v3';

export interface RealTimePrice {
  symbol: string;
  price: number;
  change24h: number;
  changePercent24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  marketCap?: number;
  lastUpdate: number;
}

export interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  ema20: number;
  ema50: number;
  volume: number;
  support: number;
  resistance: number;
}

// Obtener precio en tiempo real de Binance
export const getRealTimePrice = async (symbol: string = 'BTCUSDT'): Promise<RealTimePrice> => {
  try {
    const [ticker24h, price] = await Promise.all([
      axios.get(`${BINANCE_API}/ticker/24hr?symbol=${symbol}`),
      axios.get(`${BINANCE_API}/ticker/price?symbol=${symbol}`)
    ]);

    const data = ticker24h.data;
    
    return {
      symbol: symbol.replace('USDT', '/USD'),
      price: parseFloat(price.data.price),
      change24h: parseFloat(data.priceChange),
      changePercent24h: parseFloat(data.priceChangePercent),
      volume24h: parseFloat(data.volume),
      high24h: parseFloat(data.highPrice),
      low24h: parseFloat(data.lowPrice),
      lastUpdate: Date.now(),
    };
  } catch (error) {
    console.error('Error obteniendo precio en tiempo real:', error);
    throw error;
  }
};

// Obtener datos de velas en tiempo real
export const getRealTimeCandleData = async (
  symbol: string = 'BTCUSDT',
  interval: string = '1h',
  limit: number = 100
): Promise<CandleData[]> => {
  try {
    const response = await axios.get(`${BINANCE_API}/klines`, {
      params: {
        symbol,
        interval,
        limit,
      },
    });

    return response.data.map((candle: any[]) => ({
      timestamp: candle[0],
      open: parseFloat(candle[1]),
      high: parseFloat(candle[2]),
      low: parseFloat(candle[3]),
      close: parseFloat(candle[4]),
      volume: parseFloat(candle[5]),
    }));
  } catch (error) {
    console.error('Error obteniendo datos de velas:', error);
    throw error;
  }
};

// Calcular indicadores técnicos
export const calculateTechnicalIndicators = (candleData: CandleData[]): TechnicalIndicators => {
  const closes = candleData.map(c => c.close);
  const highs = candleData.map(c => c.high);
  const lows = candleData.map(c => c.low);
  const volumes = candleData.map(c => c.volume);

  // RSI (Relative Strength Index)
  const rsi = calculateRSI(closes, 14);

  // MACD
  const macd = calculateMACD(closes);

  // Bollinger Bands
  const bollinger = calculateBollingerBands(closes, 20, 2);

  // EMAs
  const ema20 = calculateEMA(closes, 20);
  const ema50 = calculateEMA(closes, 50);

  // Soporte y Resistencia
  const support = Math.min(...lows.slice(-20));
  const resistance = Math.max(...highs.slice(-20));

  return {
    rsi,
    macd,
    bollinger,
    ema20,
    ema50,
    volume: volumes[volumes.length - 1],
    support,
    resistance,
  };
};

// Función para calcular RSI
const calculateRSI = (prices: number[], period: number = 14): number => {
  if (prices.length < period + 1) return 50;

  let gains = 0;
  let losses = 0;

  for (let i = 1; i <= period; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) {
      gains += change;
    } else {
      losses -= change;
    }
  }

  let avgGain = gains / period;
  let avgLoss = losses / period;

  for (let i = period + 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) {
      avgGain = (avgGain * (period - 1) + change) / period;
      avgLoss = (avgLoss * (period - 1)) / period;
    } else {
      avgGain = (avgGain * (period - 1)) / period;
      avgLoss = (avgLoss * (period - 1) - change) / period;
    }
  }

  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
};

// Función para calcular MACD
const calculateMACD = (prices: number[]): { macd: number; signal: number; histogram: number } => {
  const ema12 = calculateEMA(prices, 12);
  const ema26 = calculateEMA(prices, 26);
  const macdLine = ema12 - ema26;
  
  // Para simplificar, usamos el MACD actual como señal
  const signal = macdLine * 0.9;
  const histogram = macdLine - signal;

  return {
    macd: macdLine,
    signal,
    histogram,
  };
};

// Función para calcular EMA
const calculateEMA = (prices: number[], period: number): number => {
  if (prices.length < period) return prices[prices.length - 1];

  const multiplier = 2 / (period + 1);
  let ema = prices[0];

  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
  }

  return ema;
};

// Función para calcular Bollinger Bands
const calculateBollingerBands = (prices: number[], period: number, stdDev: number) => {
  const recentPrices = prices.slice(-period);
  const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period;
  
  const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
  const standardDeviation = Math.sqrt(variance);

  return {
    upper: sma + (standardDeviation * stdDev),
    middle: sma,
    lower: sma - (standardDeviation * stdDev),
  };
};

// Obtener múltiples símbolos
export const getMultipleSymbols = async (): Promise<RealTimePrice[]> => {
  const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT'];
  
  try {
    const promises = symbols.map(symbol => getRealTimePrice(symbol));
    return await Promise.all(promises);
  } catch (error) {
    console.error('Error obteniendo múltiples símbolos:', error);
    throw error;
  }
};

// Mapear intervalos de timeframe
export const mapTimeframeToInterval = (timeframe: string): string => {
  const mapping: { [key: string]: string } = {
    '15M': '15m',
    '1H': '1h',
    '4H': '4h',
    '1D': '1d',
    '1W': '1w',
  };
  return mapping[timeframe] || '1h';
};
