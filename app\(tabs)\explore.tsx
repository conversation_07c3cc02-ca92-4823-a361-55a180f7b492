import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, TradingColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import * as Haptics from 'expo-haptics';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

interface AnalysisCardProps {
  title: string;
  description: string;
  icon: string;
  status: 'active' | 'completed' | 'pending';
  onPress: () => void;
}

const AnalysisCard: React.FC<AnalysisCardProps> = ({ title, description, icon, status, onPress }) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  
  const getStatusColor = () => {
    switch (status) {
      case 'active':
        return TradingColors.accent;
      case 'completed':
        return TradingColors.bullish;
      case 'pending':
        return TradingColors.neutral;
      default:
        return colors.secondary;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'active':
        return 'En progreso';
      case 'completed':
        return 'Completado';
      case 'pending':
        return 'Pendiente';
      default:
        return '';
    }
  };

  return (
    <TouchableOpacity
      style={[styles.analysisCard, { backgroundColor: colors.card }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.cardHeader}>
        <IconSymbol name={icon} size={24} color={getStatusColor()} />
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() }]}>
          <ThemedText style={styles.statusText}>{getStatusText()}</ThemedText>
        </View>
      </View>
      <ThemedText style={styles.cardTitle}>{title}</ThemedText>
      <ThemedText style={[styles.cardDescription, { color: colors.secondary }]}>
        {description}
      </ThemedText>
    </TouchableOpacity>
  );
};

export default function AnalysisScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  
  const [analyses] = useState([
    {
      id: 1,
      title: 'Análisis Técnico BTC',
      description: 'Evaluación de patrones de velas y indicadores técnicos para Bitcoin',
      icon: 'chart.line.uptrend.xyaxis',
      status: 'completed' as const,
    },
    {
      id: 2,
      title: 'Sentimiento del Mercado',
      description: 'Análisis de noticias y redes sociales para determinar el sentimiento',
      icon: 'brain.head.profile',
      status: 'active' as const,
    },
    {
      id: 3,
      title: 'Correlación de Activos',
      description: 'Estudio de correlaciones entre diferentes criptomonedas',
      icon: 'arrow.triangle.branch',
      status: 'pending' as const,
    },
    {
      id: 4,
      title: 'Predicción de Volatilidad',
      description: 'Modelo de machine learning para predecir volatilidad futura',
      icon: 'waveform.path.ecg',
      status: 'completed' as const,
    },
  ]);

  const handleAnalysisPress = (analysisId: number) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    console.log(`Análisis ${analysisId} seleccionado`);
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Análisis AI</ThemedText>
          <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
            Inteligencia artificial para trading
          </ThemedText>
        </ThemedView>

        <ThemedView style={[styles.summaryCard, { backgroundColor: colors.card }]}>
          <ThemedText style={styles.summaryTitle}>Resumen del Día</ThemedText>
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <ThemedText style={[styles.statValue, { color: TradingColors.bullish }]}>
                12
              </ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.secondary }]}>
                Señales Alcistas
              </ThemedText>
            </View>
            <View style={styles.statItem}>
              <ThemedText style={[styles.statValue, { color: TradingColors.bearish }]}>
                5
              </ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.secondary }]}>
                Señales Bajistas
              </ThemedText>
            </View>
            <View style={styles.statItem}>
              <ThemedText style={[styles.statValue, { color: TradingColors.accent }]}>
                87%
              </ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.secondary }]}>
                Precisión
              </ThemedText>
            </View>
          </View>
        </ThemedView>

        <ThemedText style={styles.sectionTitle}>Análisis Disponibles</ThemedText>
        
        {analyses.map((analysis) => (
          <AnalysisCard
            key={analysis.id}
            title={analysis.title}
            description={analysis.description}
            icon={analysis.icon}
            status={analysis.status}
            onPress={() => handleAnalysisPress(analysis.id)}
          />
        ))}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  summaryCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  analysisCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});
