import React from 'react';
import { StyleSheet, ScrollView, View, Switch, TouchableOpacity } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function SettingsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  const [notifications, setNotifications] = React.useState(true);
  const [darkMode, setDarkMode] = React.useState(true);
  const [hapticFeedback, setHapticFeedback] = React.useState(true);

  const SettingItem = ({ 
    icon, 
    title, 
    subtitle, 
    rightComponent, 
    onPress 
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    rightComponent?: React.ReactNode;
    onPress?: () => void;
  }) => (
    <TouchableOpacity 
      style={[styles.settingItem, { borderBottomColor: colors.border }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingLeft}>
        <IconSymbol name={icon} size={24} color={colors.primary} />
        <View style={styles.settingText}>
          <ThemedText style={styles.settingTitle}>{title}</ThemedText>
          {subtitle && (
            <ThemedText style={[styles.settingSubtitle, { color: colors.secondary }]}>
              {subtitle}
            </ThemedText>
          )}
        </View>
      </View>
      {rightComponent && (
        <View style={styles.settingRight}>
          {rightComponent}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Configuración</ThemedText>
          <ThemedText style={styles.subtitle}>Personaliza tu experiencia</ThemedText>
        </ThemedView>

        <ThemedView style={[styles.section, { backgroundColor: colors.card }]}>
          <ThemedText style={styles.sectionTitle}>Preferencias</ThemedText>
          
          <SettingItem
            icon="bell.fill"
            title="Notificaciones"
            subtitle="Recibe alertas de trading"
            rightComponent={
              <Switch
                value={notifications}
                onValueChange={setNotifications}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={notifications ? '#FFFFFF' : colors.secondary}
              />
            }
          />

          <SettingItem
            icon="moon.fill"
            title="Modo Oscuro"
            subtitle="Interfaz optimizada para trading"
            rightComponent={
              <Switch
                value={darkMode}
                onValueChange={setDarkMode}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={darkMode ? '#FFFFFF' : colors.secondary}
              />
            }
          />

          <SettingItem
            icon="iphone.radiowaves.left.and.right"
            title="Feedback Háptico"
            subtitle="Vibración en interacciones"
            rightComponent={
              <Switch
                value={hapticFeedback}
                onValueChange={setHapticFeedback}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={hapticFeedback ? '#FFFFFF' : colors.secondary}
              />
            }
          />
        </ThemedView>

        <ThemedView style={[styles.section, { backgroundColor: colors.card }]}>
          <ThemedText style={styles.sectionTitle}>Cuenta</ThemedText>
          
          <SettingItem
            icon="person.circle.fill"
            title="Perfil"
            subtitle="Gestiona tu información personal"
            rightComponent={
              <IconSymbol name="chevron.right" size={16} color={colors.secondary} />
            }
          />

          <SettingItem
            icon="key.fill"
            title="Seguridad"
            subtitle="Autenticación y privacidad"
            rightComponent={
              <IconSymbol name="chevron.right" size={16} color={colors.secondary} />
            }
          />

          <SettingItem
            icon="creditcard.fill"
            title="Métodos de Pago"
            subtitle="Gestiona tus tarjetas y cuentas"
            rightComponent={
              <IconSymbol name="chevron.right" size={16} color={colors.secondary} />
            }
          />
        </ThemedView>

        <ThemedView style={[styles.section, { backgroundColor: colors.card }]}>
          <ThemedText style={styles.sectionTitle}>Soporte</ThemedText>
          
          <SettingItem
            icon="questionmark.circle.fill"
            title="Centro de Ayuda"
            subtitle="Preguntas frecuentes y guías"
            rightComponent={
              <IconSymbol name="chevron.right" size={16} color={colors.secondary} />
            }
          />

          <SettingItem
            icon="envelope.fill"
            title="Contactar Soporte"
            subtitle="Obtén ayuda personalizada"
            rightComponent={
              <IconSymbol name="chevron.right" size={16} color={colors.secondary} />
            }
          />
        </ThemedView>

        <View style={styles.footer}>
          <ThemedText style={[styles.version, { color: colors.secondary }]}>
            Pinelo AI v1.0.0
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  section: {
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    padding: 20,
    paddingBottom: 0,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  settingRight: {
    marginLeft: 16,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  version: {
    fontSize: 14,
  },
});
