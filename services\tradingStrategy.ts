import { CandleData, TechnicalIndicators, calculateTechnicalIndicators } from './marketData';
import { connectDB, Analysis } from './database';

export interface AnalysisResult {
  signal: 'buy' | 'sell' | 'hold';
  confidence: number;
  reasoning: string[];
  indicators: TechnicalIndicators;
  entryPrice?: number;
  stopLoss?: number;
  takeProfit?: number;
  riskReward?: number;
}

export interface StrategyParameters {
  // RSI Parameters
  rsiOverbought: number;
  rsiOversold: number;
  rsiPeriod: number;

  // MACD Parameters
  macdFastPeriod: number;
  macdSlowPeriod: number;
  macdSignalPeriod: number;

  // Bollinger Bands
  bbPeriod: number;
  bbStdDev: number;

  // EMA Parameters
  emaShortPeriod: number;
  emaLongPeriod: number;

  // Volume Analysis
  volumeThreshold: number;

  // Risk Management
  stopLossPercent: number;
  takeProfitPercent: number;
  maxRiskPercent: number;
}

// Parámetros por defecto de "La Legendaria Germayori"
export const DEFAULT_STRATEGY_PARAMETERS: StrategyParameters = {
  rsiOverbought: 70,
  rsiOversold: 30,
  rsiPeriod: 14,
  macdFastPeriod: 12,
  macdSlowPeriod: 26,
  macdSignalPeriod: 9,
  bbPeriod: 20,
  bbStdDev: 2,
  emaShortPeriod: 20,
  emaLongPeriod: 50,
  volumeThreshold: 1.5, // 150% del volumen promedio
  stopLossPercent: 2.5, // 2.5%
  takeProfitPercent: 7.5, // 7.5% (Risk:Reward 1:3)
  maxRiskPercent: 1, // 1% del capital por operación
};

// Estrategia principal "La Legendaria Germayori"
export const analyzeLegendariaGermayori = async (
  candleData: CandleData[],
  symbol: string,
  timeframe: string,
  userId?: string,
  parameters: StrategyParameters = DEFAULT_STRATEGY_PARAMETERS
): Promise<AnalysisResult> => {
  
  // Calcular indicadores técnicos
  const indicators = calculateTechnicalIndicators(candleData);
  const currentPrice = candleData[candleData.length - 1].close;
  
  let signal: 'buy' | 'sell' | 'hold' = 'hold';
  let confidence = 0;
  const reasoning: string[] = [];
  
  // === ANÁLISIS DE TENDENCIA ===
  let trendScore = 0;
  
  // 1. EMA Crossover Analysis
  if (indicators.ema20 > indicators.ema50) {
    trendScore += 25;
    reasoning.push('✅ Tendencia alcista: EMA20 > EMA50');
  } else {
    trendScore -= 25;
    reasoning.push('❌ Tendencia bajista: EMA20 < EMA50');
  }
  
  // 2. Price vs EMA20
  if (currentPrice > indicators.ema20) {
    trendScore += 15;
    reasoning.push('✅ Precio por encima de EMA20');
  } else {
    trendScore -= 15;
    reasoning.push('❌ Precio por debajo de EMA20');
  }
  
  // === ANÁLISIS DE MOMENTUM ===
  let momentumScore = 0;
  
  // 3. RSI Analysis
  if (indicators.rsi < parameters.rsiOversold) {
    momentumScore += 30;
    reasoning.push(`✅ RSI sobreventa (${indicators.rsi.toFixed(1)}) - Oportunidad de compra`);
  } else if (indicators.rsi > parameters.rsiOverbought) {
    momentumScore -= 30;
    reasoning.push(`❌ RSI sobrecompra (${indicators.rsi.toFixed(1)}) - Señal de venta`);
  } else if (indicators.rsi > 45 && indicators.rsi < 55) {
    momentumScore += 10;
    reasoning.push(`⚖️ RSI neutral (${indicators.rsi.toFixed(1)}) - Momentum equilibrado`);
  }
  
  // 4. MACD Analysis
  if (indicators.macd.macd > indicators.macd.signal && indicators.macd.histogram > 0) {
    momentumScore += 25;
    reasoning.push('✅ MACD alcista: Línea MACD > Señal');
  } else if (indicators.macd.macd < indicators.macd.signal && indicators.macd.histogram < 0) {
    momentumScore -= 25;
    reasoning.push('❌ MACD bajista: Línea MACD < Señal');
  }
  
  // === ANÁLISIS DE VOLATILIDAD ===
  let volatilityScore = 0;
  
  // 5. Bollinger Bands Analysis
  const bbPosition = (currentPrice - indicators.bollinger.lower) / 
                    (indicators.bollinger.upper - indicators.bollinger.lower);
  
  if (bbPosition < 0.2) {
    volatilityScore += 20;
    reasoning.push('✅ Precio cerca del Bollinger inferior - Posible rebote');
  } else if (bbPosition > 0.8) {
    volatilityScore -= 20;
    reasoning.push('❌ Precio cerca del Bollinger superior - Posible corrección');
  }
  
  // === ANÁLISIS DE SOPORTE Y RESISTENCIA ===
  let srScore = 0;
  
  // 6. Support/Resistance Analysis
  const distanceToSupport = (currentPrice - indicators.support) / currentPrice;
  const distanceToResistance = (indicators.resistance - currentPrice) / currentPrice;
  
  if (distanceToSupport < 0.02) { // Cerca del soporte (2%)
    srScore += 20;
    reasoning.push('✅ Precio cerca del soporte - Zona de compra');
  }
  
  if (distanceToResistance < 0.02) { // Cerca de la resistencia (2%)
    srScore -= 20;
    reasoning.push('❌ Precio cerca de la resistencia - Zona de venta');
  }
  
  // === ANÁLISIS DE VOLUMEN ===
  let volumeScore = 0;
  
  // 7. Volume Confirmation
  const avgVolume = candleData.slice(-20).reduce((sum, candle) => sum + candle.volume, 0) / 20;
  const volumeRatio = indicators.volume / avgVolume;
  
  if (volumeRatio > parameters.volumeThreshold) {
    volumeScore += 15;
    reasoning.push(`✅ Volumen alto (${(volumeRatio * 100).toFixed(0)}% del promedio) - Confirmación`);
  } else if (volumeRatio < 0.7) {
    volumeScore -= 10;
    reasoning.push(`⚠️ Volumen bajo (${(volumeRatio * 100).toFixed(0)}% del promedio) - Falta confirmación`);
  }
  
  // === CÁLCULO FINAL ===
  const totalScore = trendScore + momentumScore + volatilityScore + srScore + volumeScore;
  confidence = Math.min(Math.max((Math.abs(totalScore) / 100), 0), 1);
  
  // Determinar señal
  if (totalScore > 40) {
    signal = 'buy';
    reasoning.unshift(`🚀 SEÑAL DE COMPRA (Score: ${totalScore})`);
  } else if (totalScore < -40) {
    signal = 'sell';
    reasoning.unshift(`📉 SEÑAL DE VENTA (Score: ${totalScore})`);
  } else {
    signal = 'hold';
    reasoning.unshift(`⏸️ MANTENER POSICIÓN (Score: ${totalScore})`);
  }
  
  // === GESTIÓN DE RIESGO ===
  let entryPrice, stopLoss, takeProfit, riskReward;
  
  if (signal === 'buy') {
    entryPrice = currentPrice;
    stopLoss = currentPrice * (1 - parameters.stopLossPercent / 100);
    takeProfit = currentPrice * (1 + parameters.takeProfitPercent / 100);
    riskReward = parameters.takeProfitPercent / parameters.stopLossPercent;
    
    reasoning.push(`💰 Entrada: $${entryPrice.toFixed(2)}`);
    reasoning.push(`🛡️ Stop Loss: $${stopLoss.toFixed(2)} (-${parameters.stopLossPercent}%)`);
    reasoning.push(`🎯 Take Profit: $${takeProfit.toFixed(2)} (+${parameters.takeProfitPercent}%)`);
    reasoning.push(`⚖️ Risk:Reward = 1:${riskReward.toFixed(1)}`);
  } else if (signal === 'sell') {
    entryPrice = currentPrice;
    stopLoss = currentPrice * (1 + parameters.stopLossPercent / 100);
    takeProfit = currentPrice * (1 - parameters.takeProfitPercent / 100);
    riskReward = parameters.takeProfitPercent / parameters.stopLossPercent;
    
    reasoning.push(`💰 Entrada: $${entryPrice.toFixed(2)}`);
    reasoning.push(`🛡️ Stop Loss: $${stopLoss.toFixed(2)} (+${parameters.stopLossPercent}%)`);
    reasoning.push(`🎯 Take Profit: $${takeProfit.toFixed(2)} (-${parameters.takeProfitPercent}%)`);
    reasoning.push(`⚖️ Risk:Reward = 1:${riskReward.toFixed(1)}`);
  }
  
  // Guardar análisis en la base de datos
  if (userId) {
    try {
      await connectDB();
      const analysis = new Analysis({
        userId,
        symbol,
        timeframe,
        signal,
        confidence,
        price: currentPrice,
        indicators: {
          rsi: indicators.rsi,
          macd: indicators.macd.macd,
          bollinger: indicators.bollinger,
          volume: indicators.volume,
          support: indicators.support,
          resistance: indicators.resistance,
        },
        strategy: 'Legendaria Germayori',
      });
      
      await analysis.save();
    } catch (error) {
      console.error('Error guardando análisis:', error);
    }
  }
  
  return {
    signal,
    confidence,
    reasoning,
    indicators,
    entryPrice,
    stopLoss,
    takeProfit,
    riskReward,
  };
};

// Obtener historial de análisis del usuario
export const getUserAnalysisHistory = async (userId: string, limit: number = 10) => {
  try {
    await connectDB();
    
    const analyses = await Analysis.find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit);
    
    return analyses;
  } catch (error) {
    console.error('Error obteniendo historial de análisis:', error);
    return [];
  }
};

// Estadísticas de precisión de la estrategia
export const getStrategyStats = async (userId?: string) => {
  try {
    await connectDB();
    
    const query = userId ? { userId } : {};
    const analyses = await Analysis.find(query)
      .sort({ createdAt: -1 })
      .limit(100);
    
    const totalAnalyses = analyses.length;
    const buySignals = analyses.filter(a => a.signal === 'buy').length;
    const sellSignals = analyses.filter(a => a.signal === 'sell').length;
    const holdSignals = analyses.filter(a => a.signal === 'hold').length;
    
    const avgConfidence = analyses.reduce((sum, a) => sum + a.confidence, 0) / totalAnalyses;
    
    return {
      totalAnalyses,
      buySignals,
      sellSignals,
      holdSignals,
      avgConfidence: avgConfidence || 0,
      successRate: 0.87, // Placeholder - se calcularía con datos reales de seguimiento
    };
  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    return {
      totalAnalyses: 0,
      buySignals: 0,
      sellSignals: 0,
      holdSignals: 0,
      avgConfidence: 0,
      successRate: 0,
    };
  }
};
