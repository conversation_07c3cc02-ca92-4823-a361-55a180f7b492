import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
  SafeAreaView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { TradingViewChart } from '@/components/trading/TradingViewChart';
import { TradingMetrics } from '@/components/trading/TradingMetrics';
import { Colors, TradingColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getMarketData, getRealTimePrice, MarketData, RealTimePrice } from '@/services/marketDataReal';
import * as Haptics from 'expo-haptics';

export default function TradingScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];

  const [currentSymbol, setCurrentSymbol] = useState('BINANCE:BTCUSDT');
  const [realTimeData, setRealTimeData] = useState<RealTimePrice | null>(null);
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showMetrics, setShowMetrics] = useState(true);

  // Datos por defecto mientras carga la data real
  const currentPrice = realTimeData?.price || 43250;
  const priceChange = realTimeData?.change24h || 1250;
  const priceChangePercent = realTimeData?.changePercent24h || 2.98;
  const volume24h = realTimeData?.volume24h || 28500000000;
  const high24h = realTimeData?.high24h || 43500;
  const low24h = realTimeData?.low24h || 42800;

  useEffect(() => {
    loadInitialData();
    const interval = setInterval(updateRealTimeData, 5000); // Actualizar cada 5 segundos
    return () => clearInterval(interval);
  }, [currentSymbol]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Cargar datos de mercado
      const markets = await getMarketData('crypto');
      setMarketData(markets);

      // Cargar datos en tiempo real del símbolo actual
      await updateRealTimeData();

    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'No se pudieron cargar los datos del mercado.');
    } finally {
      setLoading(false);
    }
  };

  const updateRealTimeData = async () => {
    try {
      // Extraer símbolo de Binance del formato TradingView
      const binanceSymbol = currentSymbol.includes('BINANCE:') 
        ? currentSymbol.replace('BINANCE:', '')
        : 'BTCUSDT';

      const data = await getRealTimePrice(binanceSymbol);
      setRealTimeData(data);
    } catch (error) {
      console.error('Error updating real-time data:', error);
      // No mostrar error al usuario para actualizaciones automáticas
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    try {
      await loadInitialData();
    } catch (error) {
      Alert.alert('Error', 'No se pudieron actualizar los datos.');
    } finally {
      setRefreshing(false);
    }
  };

  const handleSymbolChange = (newSymbol: string) => {
    setCurrentSymbol(newSymbol);
    setRealTimeData(null); // Limpiar datos anteriores
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const toggleMetrics = () => {
    setShowMetrics(!showMetrics);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getSymbolDisplayName = () => {
    if (currentSymbol.includes('BINANCE:')) {
      return currentSymbol.replace('BINANCE:', '').replace('USDT', '/USD');
    }
    if (currentSymbol.includes('FX:')) {
      const pair = currentSymbol.replace('FX:', '');
      return `${pair.slice(0, 3)}/${pair.slice(3)}`;
    }
    return currentSymbol;
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <View style={styles.headerLeft}>
            <ThemedText type="title" style={styles.title}>
              Trading
            </ThemedText>
            <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
              {getSymbolDisplayName()}
            </ThemedText>
          </View>
          
          <View style={styles.headerRight}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: colors.background }]}
              onPress={toggleMetrics}
            >
              <Ionicons 
                name={showMetrics ? 'eye-off' : 'eye'} 
                size={20} 
                color={colors.text} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: colors.background }]}
              onPress={onRefresh}
            >
              <Ionicons name="refresh" size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {/* Métricas de trading */}
          {showMetrics && (
            <View style={styles.metricsContainer}>
              <TradingMetrics
                symbol={getSymbolDisplayName()}
                price={currentPrice}
                change={priceChange}
                changePercent={priceChangePercent}
                volume={volume24h}
                high24h={high24h}
                low24h={low24h}
                marketCap={realTimeData ? undefined : 850000000000}
              />
            </View>
          )}

          {/* TradingView Chart con análisis GermAyori */}
          <View style={styles.chartContainer}>
            <TradingViewChart
              symbol={currentSymbol}
              interval="1H"
              theme={colorScheme === 'dark' ? 'dark' : 'light'}
              onSymbolChange={handleSymbolChange}
            />
          </View>

          {/* Información adicional */}
          <View style={[styles.infoContainer, { backgroundColor: colors.card }]}>
            <View style={styles.infoHeader}>
              <Ionicons name="information-circle" size={24} color={colors.primary} />
              <ThemedText style={styles.infoTitle}>Estrategia GermAyori</ThemedText>
            </View>
            
            <View style={styles.infoContent}>
              <View style={styles.infoItem}>
                <Ionicons name="analytics" size={16} color={colors.secondary} />
                <ThemedText style={[styles.infoText, { color: colors.secondary }]}>
                  Análisis automático en 5 temporalidades (D1, H4, H1, M15, M5)
                </ThemedText>
              </View>
              
              <View style={styles.infoItem}>
                <Ionicons name="target" size={16} color={colors.secondary} />
                <ThemedText style={[styles.infoText, { color: colors.secondary }]}>
                  Detección de Fair Value Gaps y Order Blocks
                </ThemedText>
              </View>
              
              <View style={styles.infoItem}>
                <Ionicons name="flash" size={16} color={colors.secondary} />
                <ThemedText style={[styles.infoText, { color: colors.secondary }]}>
                  Señales de entrada con Stop Loss y Take Profit precisos
                </ThemedText>
              </View>
            </View>
          </View>

          {/* Mercados populares */}
          <View style={[styles.marketsContainer, { backgroundColor: colors.card }]}>
            <ThemedText style={styles.marketsTitle}>Mercados Populares</ThemedText>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.marketsList}>
                {marketData.slice(0, 5).map((market, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.marketItem, { backgroundColor: colors.background }]}
                    onPress={() => handleSymbolChange(`BINANCE:${market.symbol.replace('/', '')}USDT`)}
                  >
                    <ThemedText style={styles.marketSymbol}>{market.symbol}</ThemedText>
                    <ThemedText style={styles.marketPrice}>
                      ${market.price.toLocaleString()}
                    </ThemedText>
                    <ThemedText
                      style={[
                        styles.marketChange,
                        { color: market.changePercent >= 0 ? '#00FF88' : '#FF4757' }
                      ]}
                    >
                      {market.changePercent >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Datos en tiempo real */}
          {realTimeData && (
            <View style={[styles.realTimeContainer, { backgroundColor: colors.card }]}>
              <View style={styles.realTimeHeader}>
                <View style={styles.liveIndicator}>
                  <View style={styles.liveDot} />
                  <ThemedText style={styles.liveText}>EN VIVO</ThemedText>
                </View>
                <ThemedText style={[styles.lastUpdate, { color: colors.secondary }]}>
                  Actualizado: {new Date(realTimeData.lastUpdate).toLocaleTimeString()}
                </ThemedText>
              </View>
              
              <View style={styles.realTimeData}>
                <View style={styles.realTimeItem}>
                  <ThemedText style={[styles.realTimeLabel, { color: colors.secondary }]}>
                    Volumen 24h
                  </ThemedText>
                  <ThemedText style={styles.realTimeValue}>
                    {(realTimeData.volume24h / 1000000).toFixed(1)}M
                  </ThemedText>
                </View>
                
                <View style={styles.realTimeItem}>
                  <ThemedText style={[styles.realTimeLabel, { color: colors.secondary }]}>
                    Máximo 24h
                  </ThemedText>
                  <ThemedText style={styles.realTimeValue}>
                    ${realTimeData.high24h.toLocaleString()}
                  </ThemedText>
                </View>
                
                <View style={styles.realTimeItem}>
                  <ThemedText style={[styles.realTimeLabel, { color: colors.secondary }]}>
                    Mínimo 24h
                  </ThemedText>
                  <ThemedText style={styles.realTimeValue}>
                    ${realTimeData.low24h.toLocaleString()}
                  </ThemedText>
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  headerRight: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  metricsContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  chartContainer: {
    height: 400,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  infoContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContent: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  marketsContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  marketsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  marketsList: {
    flexDirection: 'row',
    gap: 12,
  },
  marketItem: {
    padding: 12,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  marketSymbol: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  marketPrice: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  marketChange: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  realTimeContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  realTimeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  liveDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#00FF88',
  },
  liveText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#00FF88',
  },
  lastUpdate: {
    fontSize: 12,
  },
  realTimeData: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  realTimeItem: {
    alignItems: 'center',
  },
  realTimeLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  realTimeValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});
