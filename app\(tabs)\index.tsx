import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { AIAnalysisIndicator } from '@/components/trading/AIAnalysisIndicator';
import { CandleData, CandlestickChart } from '@/components/trading/CandlestickChart';
import { TradingMetrics } from '@/components/trading/TradingMetrics';
import { GermAyoriWelcome } from '@/components/GermAyoriWelcome';
import { Colors, TradingColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getMarketData, getRealTimePrice, getCandlestickData, MarketData, RealTimePrice } from '@/services/marketDataReal';
import * as Haptics from 'expo-haptics';
import React, { useState, useEffect } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

// Datos de ejemplo para el gráfico
const generateMockData = (): CandleData[] => {
  const data: CandleData[] = [];
  let basePrice = 42500;

  for (let i = 0; i < 30; i++) {
    const volatility = 0.02;
    const change = (Math.random() - 0.5) * volatility;
    const open = basePrice;
    const close = open * (1 + change);
    const high = Math.max(open, close) * (1 + Math.random() * 0.01);
    const low = Math.min(open, close) * (1 - Math.random() * 0.01);
    const volume = Math.random() * 1000000 + 500000;

    data.push({
      timestamp: Date.now() - (29 - i) * 3600000,
      open,
      high,
      low,
      close,
      volume,
    });

    basePrice = close;
  }

  return data;
};

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];

  const [chartData, setChartData] = useState<CandleData[]>(generateMockData());
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<{
    signal: 'buy' | 'sell' | 'hold';
    confidence: number;
  } | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('1H');

  const timeframes = ['15M', '1H', '4H', '1D', '1W'];

  const currentPrice = chartData[chartData.length - 1]?.close || 42500;
  const previousPrice = chartData[chartData.length - 2]?.close || 42000;
  const priceChange = currentPrice - previousPrice;
  const priceChangePercent = (priceChange / previousPrice) * 100;

  const startAnalysis = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsAnalyzing(true);
    setAnalysisResult(null);
  };

  const onAnalysisComplete = () => {
    setIsAnalyzing(false);
    // Simular resultado del análisis
    const signals: ('buy' | 'sell' | 'hold')[] = ['buy', 'sell', 'hold'];
    const randomSignal = signals[Math.floor(Math.random() * signals.length)];
    const confidence = Math.random() * 0.4 + 0.6; // 60-100%

    setAnalysisResult({
      signal: randomSignal,
      confidence,
    });

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  };

  const onRefresh = () => {
    setRefreshing(true);
    setChartData(generateMockData());
    setTimeout(() => setRefreshing(false), 1000);
  };

  const selectTimeframe = (timeframe: string) => {
    Haptics.selectionAsync();
    setSelectedTimeframe(timeframe);
    setChartData(generateMockData());
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Dashboard</ThemedText>
          <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
            Análisis en tiempo real
          </ThemedText>
        </ThemedView>

        {/* Métricas principales */}
        <TradingMetrics
          symbol="BTC/USD"
          price={currentPrice}
          change={priceChange}
          changePercent={priceChangePercent}
          volume={15420000}
          high24h={43200}
          low24h={41800}
          marketCap={850000000000}
        />

        {/* Indicador de análisis AI */}
        <AIAnalysisIndicator
          isAnalyzing={isAnalyzing}
          confidence={analysisResult?.confidence}
          signal={analysisResult?.signal}
          onAnalysisComplete={onAnalysisComplete}
        />

        {/* Botón de análisis */}
        {!isAnalyzing && (
          <TouchableOpacity
            style={[styles.analyzeButton, { backgroundColor: TradingColors.accent }]}
            onPress={startAnalysis}
            activeOpacity={0.8}
          >
            <ThemedText style={styles.analyzeButtonText}>
              Iniciar Análisis AI
            </ThemedText>
          </TouchableOpacity>
        )}

        {/* Selector de timeframe */}
        <View style={styles.timeframeContainer}>
          <ThemedText style={[styles.timeframeLabel, { color: colors.secondary }]}>
            Timeframe
          </ThemedText>
          <View style={styles.timeframeButtons}>
            {timeframes.map((timeframe) => (
              <TouchableOpacity
                key={timeframe}
                style={[
                  styles.timeframeButton,
                  {
                    backgroundColor: selectedTimeframe === timeframe
                      ? TradingColors.accent
                      : colors.border
                  }
                ]}
                onPress={() => selectTimeframe(timeframe)}
                activeOpacity={0.7}
              >
                <ThemedText
                  style={[
                    styles.timeframeButtonText,
                    {
                      color: selectedTimeframe === timeframe
                        ? '#FFFFFF'
                        : colors.text
                    }
                  ]}
                >
                  {timeframe}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Gráfico de velas */}
        <ThemedView style={[styles.chartContainer, { backgroundColor: colors.card }]}>
          <ThemedText style={styles.chartTitle}>
            BTC/USD - {selectedTimeframe}
          </ThemedText>
          <CandlestickChart
            data={chartData}
            height={300}
            showVolume={true}
          />
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  analyzeButton: {
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  analyzeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  timeframeContainer: {
    marginBottom: 20,
  },
  timeframeLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  timeframeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  timeframeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  timeframeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
});
