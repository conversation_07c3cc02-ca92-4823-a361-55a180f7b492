import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { TradingMetrics } from '@/components/trading/TradingMetrics';
import { Colors, TradingColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { MarketData, RealTimePrice } from '@/services/marketDataReal';
import * as Haptics from 'expo-haptics';
import React, { useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

// Interfaz para los datos de velas
interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Datos de ejemplo para el gráfico
const generateMockData = (): CandleData[] => {
  const data: CandleData[] = [];
  let basePrice = 42500;

  for (let i = 0; i < 30; i++) {
    const volatility = 0.02;
    const change = (Math.random() - 0.5) * volatility;
    const open = basePrice;
    const close = open * (1 + change);
    const high = Math.max(open, close) * (1 + Math.random() * 0.01);
    const low = Math.min(open, close) * (1 - Math.random() * 0.01);
    const volume = Math.random() * 1000000 + 500000;

    data.push({
      timestamp: Date.now() - (29 - i) * 3600000,
      open,
      high,
      low,
      close,
      volume,
    });

    basePrice = close;
  }

  return data;
};

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];

  const [chartData, setChartData] = useState<CandleData[]>(generateMockData());
  const [realTimeData, setRealTimeData] = useState<RealTimePrice | null>(null);
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<{
    signal: 'buy' | 'sell' | 'hold';
    confidence: number;
  } | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('1H');
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT');
  const [loading, setLoading] = useState(true);

  const timeframes = ['15M', '1H', '4H', '1D', '1W'];

  // Usar datos reales si están disponibles, sino usar mock data
  const currentPrice = realTimeData?.price || chartData[chartData.length - 1]?.close || 42500;
  const previousPrice = chartData[chartData.length - 2]?.close || 42000;
  const priceChange = realTimeData?.change24h || (currentPrice - previousPrice);
  const priceChangePercent = realTimeData?.changePercent24h || ((priceChange / previousPrice) * 100);

  const startAnalysis = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsAnalyzing(true);
    setAnalysisResult(null);
  };

  const onAnalysisComplete = () => {
    setIsAnalyzing(false);
    // Simular resultado del análisis
    const signals: ('buy' | 'sell' | 'hold')[] = ['buy', 'sell', 'hold'];
    const randomSignal = signals[Math.floor(Math.random() * signals.length)];
    const confidence = Math.random() * 0.4 + 0.6; // 60-100%

    setAnalysisResult({
      signal: randomSignal,
      confidence,
    });

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  };

  const onRefresh = () => {
    setRefreshing(true);
    setChartData(generateMockData());
    setTimeout(() => setRefreshing(false), 1000);
  };

  const selectTimeframe = (timeframe: string) => {
    Haptics.selectionAsync();
    setSelectedTimeframe(timeframe);
    setChartData(generateMockData());
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Dashboard</ThemedText>
          <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
            Análisis en tiempo real
          </ThemedText>
        </ThemedView>

        {/* Métricas principales */}
        <TradingMetrics
          symbol="BTC/USD"
          price={currentPrice}
          change={priceChange}
          changePercent={priceChangePercent}
          volume={15420000}
          high24h={43200}
          low24h={41800}
          marketCap={850000000000}
        />

        {/* Indicador de análisis AI */}
        {isAnalyzing && (
          <ThemedView style={[styles.analysisIndicator, { backgroundColor: colors.card }]}>
            <ThemedText style={styles.analysisText}>
              🧠 Analizando con IA GermAyori...
            </ThemedText>
          </ThemedView>
        )}

        {analysisResult && (
          <ThemedView style={[styles.analysisResult, { backgroundColor: colors.card }]}>
            <ThemedText style={styles.analysisTitle}>
              📊 Resultado del Análisis
            </ThemedText>
            <ThemedText style={[
              styles.signalText,
              { color: analysisResult.signal === 'buy' ? TradingColors.success :
                       analysisResult.signal === 'sell' ? TradingColors.danger :
                       colors.text }
            ]}>
              Señal: {analysisResult.signal.toUpperCase()}
            </ThemedText>
            <ThemedText style={styles.confidenceText}>
              Confianza: {(analysisResult.confidence * 100).toFixed(1)}%
            </ThemedText>
          </ThemedView>
        )}

        {/* Botón de análisis */}
        {!isAnalyzing && (
          <TouchableOpacity
            style={[styles.analyzeButton, { backgroundColor: TradingColors.accent }]}
            onPress={startAnalysis}
            activeOpacity={0.8}
          >
            <ThemedText style={styles.analyzeButtonText}>
              Iniciar Análisis AI
            </ThemedText>
          </TouchableOpacity>
        )}

        {/* Selector de timeframe */}
        <View style={styles.timeframeContainer}>
          <ThemedText style={[styles.timeframeLabel, { color: colors.secondary }]}>
            Timeframe
          </ThemedText>
          <View style={styles.timeframeButtons}>
            {timeframes.map((timeframe) => (
              <TouchableOpacity
                key={timeframe}
                style={[
                  styles.timeframeButton,
                  {
                    backgroundColor: selectedTimeframe === timeframe
                      ? TradingColors.accent
                      : colors.border
                  }
                ]}
                onPress={() => selectTimeframe(timeframe)}
                activeOpacity={0.7}
              >
                <ThemedText
                  style={[
                    styles.timeframeButtonText,
                    {
                      color: selectedTimeframe === timeframe
                        ? '#FFFFFF'
                        : colors.text
                    }
                  ]}
                >
                  {timeframe}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Gráfico de velas */}
        <ThemedView style={[styles.chartContainer, { backgroundColor: colors.card }]}>
          <ThemedText style={styles.chartTitle}>
            BTC/USD - {selectedTimeframe}
          </ThemedText>
          <ThemedView style={styles.chartPlaceholder}>
            <ThemedText style={[styles.chartPlaceholderText, { color: colors.secondary }]}>
              📈 Gráfico de Velas
            </ThemedText>
            <ThemedText style={[styles.chartSubtext, { color: colors.secondary }]}>
              Precio: ${currentPrice.toLocaleString()}
            </ThemedText>
            <ThemedText style={[styles.chartSubtext, {
              color: priceChangePercent >= 0 ? TradingColors.success : TradingColors.danger
            }]}>
              {priceChangePercent >= 0 ? '↗' : '↘'} {priceChangePercent.toFixed(2)}%
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  analyzeButton: {
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  analyzeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  timeframeContainer: {
    marginBottom: 20,
  },
  timeframeLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  timeframeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  timeframeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  timeframeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  analysisIndicator: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  analysisText: {
    fontSize: 16,
    fontWeight: '600',
  },
  analysisResult: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  signalText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  confidenceText: {
    fontSize: 14,
    textAlign: 'center',
  },
  chartPlaceholder: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#666',
  },
  chartPlaceholderText: {
    fontSize: 24,
    marginBottom: 8,
  },
  chartSubtext: {
    fontSize: 16,
    marginBottom: 4,
  },
});
