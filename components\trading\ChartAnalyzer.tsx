import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  Modal,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
  selectImage,
  takePhoto,
  analyzeChartWithGermAyori,
  formatAnalysisForDisplay,
  GermAyoriAnalysis
} from '@/services/germAyoriStrategy';
import * as Haptics from 'expo-haptics';

const { width, height } = Dimensions.get('window');

interface ChartAnalyzerProps {
  onAnalysisComplete?: (analysis: GermAyoriAnalysis) => void;
}

export const ChartAnalyzer: React.FC<ChartAnalyzerProps> = ({ onAnalysisComplete }) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<GermAyoriAnalysis | null>(null);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);

  const handleSelectImage = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      const imageBase64 = await selectImage();
      if (imageBase64) {
        setSelectedImage(imageBase64);
        setAnalysis(null);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo seleccionar la imagen.');
    }
  };

  const handleTakePhoto = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      const imageBase64 = await takePhoto();
      if (imageBase64) {
        setSelectedImage(imageBase64);
        setAnalysis(null);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo tomar la foto.');
    }
  };

  const handleAnalyzeChart = async () => {
    if (!selectedImage) {
      Alert.alert('Error', 'Primero selecciona o toma una foto del gráfico.');
      return;
    }

    try {
      setIsAnalyzing(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const result = await analyzeChartWithGermAyori(selectedImage);
      setAnalysis(result);
      setShowAnalysisModal(true);
      
      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }

      Haptics.notificationAsync(
        result.isValidEntry 
          ? Haptics.NotificationFeedbackType.Success 
          : Haptics.NotificationFeedbackType.Warning
      );

    } catch (error) {
      Alert.alert('Error', 'No se pudo analizar el gráfico. Inténtalo de nuevo.');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const clearImage = () => {
    setSelectedImage(null);
    setAnalysis(null);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getSignalColor = (signal?: string) => {
    switch (signal) {
      case 'BUY': return '#00FF88';
      case 'SELL': return '#FF4757';
      default: return '#FFD700';
    }
  };

  const getSignalIcon = (signal?: string) => {
    switch (signal) {
      case 'BUY': return 'trending-up';
      case 'SELL': return 'trending-down';
      default: return 'help-circle';
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <ThemedText type="title" style={styles.title}>
          Análisis GermAyori
        </ThemedText>
        <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
          Sube un gráfico para análisis con IA
        </ThemedText>
      </View>

      {/* Botones de acción */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.card }]}
          onPress={handleSelectImage}
          activeOpacity={0.8}
        >
          <Ionicons name="image" size={24} color={colors.primary} />
          <ThemedText style={styles.actionButtonText}>Galería</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.card }]}
          onPress={handleTakePhoto}
          activeOpacity={0.8}
        >
          <Ionicons name="camera" size={24} color={colors.primary} />
          <ThemedText style={styles.actionButtonText}>Cámara</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Imagen seleccionada */}
      {selectedImage && (
        <View style={styles.imageContainer}>
          <View style={styles.imageWrapper}>
            <Image
              source={{ uri: `data:image/jpeg;base64,${selectedImage}` }}
              style={styles.selectedImage}
              resizeMode="contain"
            />
            <TouchableOpacity
              style={styles.clearButton}
              onPress={clearImage}
            >
              <Ionicons name="close-circle" size={24} color="#FF4757" />
            </TouchableOpacity>
          </View>

          {/* Botón de análisis */}
          <TouchableOpacity
            style={[
              styles.analyzeButton,
              { backgroundColor: isAnalyzing ? colors.secondary : '#FFD700' }
            ]}
            onPress={handleAnalyzeChart}
            disabled={isAnalyzing}
            activeOpacity={0.8}
          >
            {isAnalyzing ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#000000" />
                <Text style={styles.analyzeButtonText}>Analizando...</Text>
              </View>
            ) : (
              <>
                <Ionicons name="analytics" size={20} color="#000000" />
                <Text style={styles.analyzeButtonText}>Analizar con GermAyori</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Resultado del análisis */}
      {analysis && (
        <View style={[styles.resultContainer, { backgroundColor: colors.card }]}>
          <View style={styles.resultHeader}>
            <View style={styles.resultStatus}>
              <Ionicons
                name={analysis.isValidEntry ? 'checkmark-circle' : 'close-circle'}
                size={24}
                color={analysis.isValidEntry ? '#00FF88' : '#FF4757'}
              />
              <ThemedText style={styles.resultTitle}>
                {analysis.isValidEntry ? 'Entrada Válida' : 'Sin Entrada Clara'}
              </ThemedText>
            </View>
            <View style={[styles.confidenceBadge, { backgroundColor: colors.background }]}>
              <ThemedText style={styles.confidenceText}>
                {analysis.confidence}%
              </ThemedText>
            </View>
          </View>

          {analysis.entry && (
            <View style={styles.entryDetails}>
              <View style={[styles.signalBadge, { backgroundColor: getSignalColor(analysis.entry.type) }]}>
                <Ionicons name={getSignalIcon(analysis.entry.type) as any} size={16} color="#FFFFFF" />
                <Text style={styles.signalText}>{analysis.entry.type}</Text>
              </View>
              
              <View style={styles.priceDetails}>
                <View style={styles.priceItem}>
                  <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>Entrada</ThemedText>
                  <ThemedText style={styles.priceValue}>{analysis.entry.price}</ThemedText>
                </View>
                <View style={styles.priceItem}>
                  <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>TP</ThemedText>
                  <ThemedText style={[styles.priceValue, { color: '#00FF88' }]}>
                    {analysis.entry.takeProfit}
                  </ThemedText>
                </View>
                <View style={styles.priceItem}>
                  <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>SL</ThemedText>
                  <ThemedText style={[styles.priceValue, { color: '#FF4757' }]}>
                    {analysis.entry.stopLoss}
                  </ThemedText>
                </View>
                <View style={styles.priceItem}>
                  <ThemedText style={[styles.priceLabel, { color: colors.secondary }]}>RR</ThemedText>
                  <ThemedText style={[styles.priceValue, { color: '#FFD700' }]}>
                    1:{analysis.entry.riskReward}
                  </ThemedText>
                </View>
              </View>
            </View>
          )}

          <TouchableOpacity
            style={styles.viewDetailsButton}
            onPress={() => setShowAnalysisModal(true)}
          >
            <ThemedText style={[styles.viewDetailsText, { color: colors.primary }]}>
              Ver análisis completo
            </ThemedText>
            <Ionicons name="chevron-forward" size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Modal de análisis completo */}
      <Modal
        visible={showAnalysisModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAnalysisModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <ThemedText type="title" style={styles.modalTitle}>
              Análisis GermAyori Completo
            </ThemedText>
            <TouchableOpacity onPress={() => setShowAnalysisModal(false)}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {analysis && (
              <View style={styles.analysisContent}>
                <Text style={[styles.analysisText, { color: colors.text }]}>
                  {formatAnalysisForDisplay(analysis)}
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>

      {/* Instrucciones */}
      {!selectedImage && (
        <View style={styles.instructionsContainer}>
          <View style={styles.instructionItem}>
            <Ionicons name="camera" size={20} color={colors.primary} />
            <ThemedText style={[styles.instructionText, { color: colors.secondary }]}>
              Toma una foto del gráfico o selecciona de tu galería
            </ThemedText>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="analytics" size={20} color={colors.primary} />
            <ThemedText style={[styles.instructionText, { color: colors.secondary }]}>
              La IA analizará usando la estrategia GermAyori FVG
            </ThemedText>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
            <ThemedText style={[styles.instructionText, { color: colors.secondary }]}>
              Recibe análisis detallado con puntos de entrada precisos
            </ThemedText>
          </View>
        </View>
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  imageContainer: {
    marginBottom: 20,
  },
  imageWrapper: {
    position: 'relative',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  selectedImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  clearButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  analyzeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
  },
  resultContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  confidenceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  confidenceText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  entryDetails: {
    marginBottom: 12,
  },
  signalBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
    marginBottom: 12,
  },
  signalText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  priceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceItem: {
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    paddingVertical: 8,
  },
  viewDetailsText: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  analysisContent: {
    marginBottom: 20,
  },
  analysisText: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: 'monospace',
  },
  instructionsContainer: {
    gap: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  instructionText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
});
