{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    // Puedes usar 'customConditions' solo si 'moduleResolution' es 'node16', 'nodenext' o 'bundler'
    "jsx": "react-native",
    "module": "Node16",
    "moduleResolution": "node16",
    "allowJs": true,
    "esModuleInterop": true,
    "strict": true,
    "paths": {
      "@/*": [
        "./*"
      ]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts"
  ]
}
