import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
  SafeAreaView
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

interface GermAyoriWelcomeProps {
  onContinue: () => void;
}

export const GermAyoriWelcome: React.FC<GermAyoriWelcomeProps> = ({ onContinue }) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    // Animación de entrada
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 1200,
        useNativeDriver: true,
      }),
    ]).start();

    // Animación de pulso para el botón
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      {/* Fondo negro con gradiente */}
      <LinearGradient
        colors={['#000000', '#1a1a1a', '#000000']}
        style={styles.background}
      >
        <Animated.View 
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          {/* Logo GermAyori */}
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>GermAyori</Text>
            <View style={styles.logoUnderline} />
          </View>

          {/* Título principal */}
          <Text style={styles.mainTitle}>
            Deja que la IA lea{'\n'}tus gráficos
          </Text>

          {/* Botón principal */}
          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <TouchableOpacity 
              style={styles.mainButton}
              onPress={onContinue}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#FFD700', '#FFA500']}
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={styles.buttonText}>Pruébalo ahora</Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Sección de Ideas Clave */}
          <View style={styles.ideasContainer}>
            <Text style={styles.ideasTitle}>Ideas clave</Text>
            
            <View style={styles.ideasGrid}>
              {/* Tendencia Alcista */}
              <View style={styles.ideaCard}>
                <View style={styles.ideaIcon}>
                  <Ionicons name="trending-up" size={24} color="#00FF88" />
                </View>
                <Text style={styles.ideaLabel}>Tendencia</Text>
                <Text style={styles.ideaValue}>Alcista</Text>
                <View style={styles.trendLine} />
              </View>

              {/* Volatilidad Alto */}
              <View style={styles.ideaCard}>
                <View style={styles.ideaIcon}>
                  <Ionicons name="pulse" size={24} color="#FF6B35" />
                </View>
                <Text style={styles.ideaLabel}>Volatilidad</Text>
                <Text style={styles.ideaValue}>Alto</Text>
                <View style={styles.volatilityWave} />
              </View>

              {/* Volumen Medio */}
              <View style={styles.ideaCardLarge}>
                <View style={styles.ideaIcon}>
                  <Ionicons name="bar-chart" size={24} color="#4A90E2" />
                </View>
                <Text style={styles.ideaLabel}>Volumen</Text>
                <Text style={styles.ideaValue}>Medio</Text>
                <View style={styles.volumeBars}>
                  <View style={[styles.volumeBar, { height: 20, backgroundColor: '#4A90E2' }]} />
                  <View style={[styles.volumeBar, { height: 35, backgroundColor: '#5BA0F2' }]} />
                  <View style={[styles.volumeBar, { height: 25, backgroundColor: '#6BB0FF' }]} />
                  <View style={[styles.volumeBar, { height: 40, backgroundColor: '#7BC0FF' }]} />
                </View>
              </View>
            </View>
          </View>

          {/* Información adicional */}
          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Ionicons name="shield-checkmark" size={16} color="#FFD700" />
              <Text style={styles.infoText}>Datos en tiempo real</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="analytics" size={16} color="#FFD700" />
              <Text style={styles.infoText}>Análisis con IA</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="flash" size={16} color="#FFD700" />
              <Text style={styles.infoText}>Alertas instantáneas</Text>
            </View>
          </View>
        </Animated.View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  logoText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFD700',
    textAlign: 'center',
    letterSpacing: 2,
  },
  logoUnderline: {
    width: 100,
    height: 3,
    backgroundColor: '#FFD700',
    marginTop: 5,
  },
  mainTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 40,
    marginVertical: 20,
  },
  mainButton: {
    marginVertical: 20,
  },
  buttonGradient: {
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 200,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  ideasContainer: {
    width: '100%',
    marginVertical: 20,
  },
  ideasTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 20,
    textAlign: 'center',
  },
  ideasGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 15,
  },
  ideaCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 15,
    width: (width - 60) / 2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  ideaCardLarge: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 15,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginTop: 10,
  },
  ideaIcon: {
    marginBottom: 10,
  },
  ideaLabel: {
    fontSize: 12,
    color: '#CCCCCC',
    marginBottom: 5,
  },
  ideaValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
  },
  trendLine: {
    width: 40,
    height: 2,
    backgroundColor: '#00FF88',
    transform: [{ rotate: '15deg' }],
  },
  volatilityWave: {
    width: 40,
    height: 20,
    borderTopWidth: 2,
    borderTopColor: '#FF6B35',
    borderRadius: 20,
  },
  volumeBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 3,
    height: 40,
  },
  volumeBar: {
    width: 6,
    borderRadius: 3,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  infoText: {
    fontSize: 12,
    color: '#CCCCCC',
  },
});

export default GermAyoriWelcome;
