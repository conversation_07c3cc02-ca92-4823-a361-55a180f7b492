import { connectDB, User, Payment } from './database';

export interface PaymentData {
  userId: string;
  amount: number;
  method: 'yappy' | 'tarjeta' | 'transferencia';
  yapyReference?: string;
}

export interface PaymentResult {
  success: boolean;
  message: string;
  transactionId?: string;
  paymentUrl?: string;
}

// Configuración de precios
export const SUBSCRIPTION_PRICE = 30; // USD por 30 días
export const YAPPY_MERCHANT_ID = 'LA-LEGENDARIA-GERMAYORI';

// Crear pago con Yappy
export const createYappyPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    await connectDB();

    // Verificar que el usuario existe
    const user = await User.findById(paymentData.userId);
    if (!user) {
      return {
        success: false,
        message: 'Usuario no encontrado',
      };
    }

    // Generar ID de transacción único
    const transactionId = `LG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Crear registro de pago
    const payment = new Payment({
      userId: paymentData.userId,
      amount: SUBSCRIPTION_PRICE,
      method: 'yappy',
      transactionId,
      yapyReference: paymentData.yapyReference,
      status: 'pending',
    });

    await payment.save();

    // Generar URL de pago de Yappy
    const yapyPaymentUrl = generateYappyPaymentUrl({
      merchantId: YAPPY_MERCHANT_ID,
      amount: SUBSCRIPTION_PRICE,
      transactionId,
      description: 'Suscripción La Legendaria Germayori - 30 días',
      customerEmail: user.email,
      customerName: user.nombreCompleto,
    });

    return {
      success: true,
      message: 'Pago creado exitosamente',
      transactionId,
      paymentUrl: yapyPaymentUrl,
    };
  } catch (error) {
    console.error('Error creando pago Yappy:', error);
    return {
      success: false,
      message: 'Error procesando el pago',
    };
  }
};

// Generar URL de pago de Yappy
const generateYappyPaymentUrl = (params: {
  merchantId: string;
  amount: number;
  transactionId: string;
  description: string;
  customerEmail: string;
  customerName: string;
}): string => {
  // URL base de Yappy para pagos
  const baseUrl = 'https://yappy.com.pa/pay';
  
  const queryParams = new URLSearchParams({
    merchant: params.merchantId,
    amount: params.amount.toString(),
    reference: params.transactionId,
    description: params.description,
    email: params.customerEmail,
    name: params.customerName,
    currency: 'USD',
    // URL de retorno después del pago
    return_url: 'https://la-legendaria-germayori.com/payment-success',
    cancel_url: 'https://la-legendaria-germayori.com/payment-cancel',
  });

  return `${baseUrl}?${queryParams.toString()}`;
};

// Confirmar pago (webhook de Yappy)
export const confirmPayment = async (transactionId: string, yapyReference: string): Promise<PaymentResult> => {
  try {
    await connectDB();

    // Buscar el pago
    const payment = await Payment.findOne({ transactionId });
    if (!payment) {
      return {
        success: false,
        message: 'Pago no encontrado',
      };
    }

    // Actualizar estado del pago
    payment.status = 'completed';
    payment.yapyReference = yapyReference;
    payment.completedAt = new Date();
    await payment.save();

    // Activar suscripción del usuario
    const user = await User.findById(payment.userId);
    if (user) {
      const now = new Date();
      const vencimiento = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)); // 30 días

      user.suscripcionActiva = true;
      user.fechaSuscripcion = now;
      user.fechaVencimiento = vencimiento;
      user.metodoPago = 'yappy';
      user.transaccionId = transactionId;
      
      await user.save();
    }

    return {
      success: true,
      message: 'Pago confirmado y suscripción activada',
      transactionId,
    };
  } catch (error) {
    console.error('Error confirmando pago:', error);
    return {
      success: false,
      message: 'Error confirmando el pago',
    };
  }
};

// Verificar estado de pago
export const checkPaymentStatus = async (transactionId: string): Promise<{
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment?: any;
}> => {
  try {
    await connectDB();
    
    const payment = await Payment.findOne({ transactionId });
    if (!payment) {
      return { status: 'failed' };
    }

    return {
      status: payment.status,
      payment: {
        id: payment._id,
        amount: payment.amount,
        method: payment.method,
        createdAt: payment.createdAt,
        completedAt: payment.completedAt,
      },
    };
  } catch (error) {
    console.error('Error verificando estado de pago:', error);
    return { status: 'failed' };
  }
};

// Verificar si la suscripción está activa
export const checkSubscriptionStatus = async (userId: string): Promise<{
  active: boolean;
  daysRemaining?: number;
  expirationDate?: Date;
}> => {
  try {
    await connectDB();
    
    const user = await User.findById(userId);
    if (!user || !user.suscripcionActiva || !user.fechaVencimiento) {
      return { active: false };
    }

    const now = new Date();
    const expirationDate = new Date(user.fechaVencimiento);
    
    if (now > expirationDate) {
      // Suscripción expirada, desactivar
      user.suscripcionActiva = false;
      await user.save();
      
      return { active: false };
    }

    const daysRemaining = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    return {
      active: true,
      daysRemaining,
      expirationDate,
    };
  } catch (error) {
    console.error('Error verificando suscripción:', error);
    return { active: false };
  }
};

// Obtener historial de pagos del usuario
export const getUserPaymentHistory = async (userId: string): Promise<any[]> => {
  try {
    await connectDB();
    
    const payments = await Payment.find({ userId })
      .sort({ createdAt: -1 })
      .limit(10);

    return payments.map(payment => ({
      id: payment._id,
      amount: payment.amount,
      method: payment.method,
      status: payment.status,
      description: payment.description,
      createdAt: payment.createdAt,
      completedAt: payment.completedAt,
    }));
  } catch (error) {
    console.error('Error obteniendo historial de pagos:', error);
    return [];
  }
};

// Procesar pago con transferencia bancaria
export const createBankTransferPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    await connectDB();

    const transactionId = `LG-BANK-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const payment = new Payment({
      userId: paymentData.userId,
      amount: SUBSCRIPTION_PRICE,
      method: 'transferencia',
      transactionId,
      status: 'pending',
    });

    await payment.save();

    return {
      success: true,
      message: 'Instrucciones de transferencia enviadas',
      transactionId,
    };
  } catch (error) {
    console.error('Error creando pago por transferencia:', error);
    return {
      success: false,
      message: 'Error procesando el pago',
    };
  }
};

// Información bancaria para transferencias
export const getBankTransferInfo = () => {
  return {
    bankName: 'Banco General',
    accountNumber: '04-01-01-*********',
    accountHolder: 'La Legendaria Germayori S.A.',
    routingNumber: '0401',
    swiftCode: 'BGENPA22',
    amount: SUBSCRIPTION_PRICE,
    currency: 'USD',
    reference: 'Suscripción La Legendaria Germayori',
    instructions: [
      '1. Realiza la transferencia por el monto exacto de $30.00 USD',
      '2. Usa como referencia tu email de registro',
      '3. Envía el <NAME_EMAIL>',
      '4. Tu suscripción se activará en 24-48 horas hábiles',
    ],
  };
};
