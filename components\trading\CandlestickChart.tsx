import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Svg, { Rect, Line, Text as SvgText } from 'react-native-svg';
import { TradingColors } from '@/constants/Colors';

const { width: screenWidth } = Dimensions.get('window');

export interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface CandlestickChartProps {
  data: CandleData[];
  width?: number;
  height?: number;
  showVolume?: boolean;
}

export function CandlestickChart({ 
  data, 
  width = screenWidth - 32, 
  height = 300,
  showVolume = true 
}: CandlestickChartProps) {
  if (!data || data.length === 0) {
    return <View style={[styles.container, { width, height }]} />;
  }

  const chartHeight = showVolume ? height * 0.7 : height;
  const volumeHeight = showVolume ? height * 0.3 : 0;
  
  // Calcular rangos de precios
  const prices = data.flatMap(d => [d.high, d.low]);
  const maxPrice = Math.max(...prices);
  const minPrice = Math.min(...prices);
  const priceRange = maxPrice - minPrice;
  
  // Calcular rango de volumen
  const maxVolume = Math.max(...data.map(d => d.volume));
  
  const candleWidth = (width - 40) / data.length;
  const candleSpacing = candleWidth * 0.8;

  const renderCandle = (candle: CandleData, index: number) => {
    const x = 20 + index * candleWidth + candleWidth / 2;
    const isGreen = candle.close > candle.open;
    
    // Calcular posiciones Y
    const highY = 20 + ((maxPrice - candle.high) / priceRange) * (chartHeight - 40);
    const lowY = 20 + ((maxPrice - candle.low) / priceRange) * (chartHeight - 40);
    const openY = 20 + ((maxPrice - candle.open) / priceRange) * (chartHeight - 40);
    const closeY = 20 + ((maxPrice - candle.close) / priceRange) * (chartHeight - 40);
    
    const bodyTop = Math.min(openY, closeY);
    const bodyHeight = Math.abs(closeY - openY);
    const bodyColor = isGreen ? TradingColors.bullish : TradingColors.bearish;
    
    return (
      <React.Fragment key={index}>
        {/* Mecha superior e inferior */}
        <Line
          x1={x}
          y1={highY}
          x2={x}
          y2={lowY}
          stroke={bodyColor}
          strokeWidth={1}
        />
        
        {/* Cuerpo de la vela */}
        <Rect
          x={x - candleSpacing / 2}
          y={bodyTop}
          width={candleSpacing}
          height={Math.max(bodyHeight, 1)}
          fill={bodyColor}
          stroke={bodyColor}
          strokeWidth={1}
        />
      </React.Fragment>
    );
  };

  const renderVolumeBar = (candle: CandleData, index: number) => {
    if (!showVolume) return null;
    
    const x = 20 + index * candleWidth + candleWidth / 2;
    const volumeRatio = candle.volume / maxVolume;
    const barHeight = volumeRatio * (volumeHeight - 20);
    const barY = chartHeight + 10 + (volumeHeight - 20 - barHeight);
    
    const isGreen = candle.close > candle.open;
    const barColor = isGreen ? TradingColors.bullish : TradingColors.bearish;
    
    return (
      <Rect
        key={`volume-${index}`}
        x={x - candleSpacing / 2}
        y={barY}
        width={candleSpacing}
        height={barHeight}
        fill={barColor}
        opacity={0.6}
      />
    );
  };

  const renderGridLines = () => {
    const gridLines = [];
    const numLines = 5;
    
    // Líneas horizontales de precio
    for (let i = 0; i <= numLines; i++) {
      const y = 20 + (i * (chartHeight - 40)) / numLines;
      const price = maxPrice - (i * priceRange) / numLines;
      
      gridLines.push(
        <React.Fragment key={`grid-${i}`}>
          <Line
            x1={20}
            y1={y}
            x2={width - 20}
            y2={y}
            stroke={TradingColors.grid}
            strokeWidth={0.5}
            opacity={0.3}
          />
          <SvgText
            x={width - 15}
            y={y + 4}
            fontSize={10}
            fill={TradingColors.grid}
            textAnchor="end"
          >
            {price.toFixed(2)}
          </SvgText>
        </React.Fragment>
      );
    }
    
    return gridLines;
  };

  return (
    <View style={[styles.container, { width, height }]}>
      <Svg width={width} height={height}>
        {renderGridLines()}
        {data.map((candle, index) => renderCandle(candle, index))}
        {showVolume && data.map((candle, index) => renderVolumeBar(candle, index))}
      </Svg>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
});
