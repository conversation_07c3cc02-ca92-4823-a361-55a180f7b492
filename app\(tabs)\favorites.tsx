import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
  Modal,
  TextInput,
  SafeAreaView
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

interface SavedAnalysis {
  id: string;
  title: string;
  description: string;
  symbol: string;
  signal: 'buy' | 'sell' | 'hold';
  confidence: number;
  timestamp: number;
  position: { x: number; y: number };
}

export default function FavoritesScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'dark'];
  
  const [savedAnalyses, setSavedAnalyses] = useState<SavedAnalysis[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<{ x: number; y: number } | null>(null);
  const [newAnalysis, setNewAnalysis] = useState({
    title: '',
    description: '',
    symbol: 'BTC/USD',
    signal: 'buy' as 'buy' | 'sell' | 'hold',
    confidence: 85
  });

  useEffect(() => {
    loadSavedAnalyses();
  }, []);

  const loadSavedAnalyses = async () => {
    try {
      const saved = await AsyncStorage.getItem('savedAnalyses');
      if (saved) {
        setSavedAnalyses(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading saved analyses:', error);
    }
  };

  const saveAnalyses = async (analyses: SavedAnalysis[]) => {
    try {
      await AsyncStorage.setItem('savedAnalyses', JSON.stringify(analyses));
      setSavedAnalyses(analyses);
    } catch (error) {
      console.error('Error saving analyses:', error);
    }
  };

  const handleImagePress = (event: any) => {
    const { locationX, locationY } = event.nativeEvent;
    setSelectedPosition({ x: locationX, y: locationY });
    setShowAddModal(true);
  };

  const saveAnalysis = () => {
    if (!newAnalysis.title.trim() || !selectedPosition) return;

    const analysis: SavedAnalysis = {
      id: Date.now().toString(),
      title: newAnalysis.title,
      description: newAnalysis.description,
      symbol: newAnalysis.symbol,
      signal: newAnalysis.signal,
      confidence: newAnalysis.confidence,
      timestamp: Date.now(),
      position: selectedPosition
    };

    const updatedAnalyses = [...savedAnalyses, analysis];
    saveAnalyses(updatedAnalyses);
    
    setShowAddModal(false);
    setNewAnalysis({
      title: '',
      description: '',
      symbol: 'BTC/USD',
      signal: 'buy',
      confidence: 85
    });
    setSelectedPosition(null);
  };

  const deleteAnalysis = (id: string) => {
    Alert.alert(
      'Eliminar Análisis',
      '¿Estás seguro de que quieres eliminar este análisis?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => {
            const updatedAnalyses = savedAnalyses.filter(a => a.id !== id);
            saveAnalyses(updatedAnalyses);
          }
        }
      ]
    );
  };

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'buy': return '#00FF88';
      case 'sell': return '#FF4757';
      case 'hold': return '#FFD700';
      default: return '#FFFFFF';
    }
  };

  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case 'buy': return 'trending-up';
      case 'sell': return 'trending-down';
      case 'hold': return 'pause';
      default: return 'help';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Favoritos</ThemedText>
          <ThemedText style={[styles.subtitle, { color: colors.secondary }]}>
            Tus análisis guardados con GermAyori
          </ThemedText>
        </ThemedView>

        {/* GermAyori Background con contraste reducido */}
        <View style={styles.germAyoriContainer}>
          <View style={styles.germAyoriBackground}>
            {/* Simulando la imagen de GermAyori con contraste reducido */}
            <LinearGradient
              colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.5)', 'rgba(0,0,0,0.7)']}
              style={styles.imageOverlay}
            >
              {/* Logo GermAyori con contraste reducido */}
              <View style={styles.logoContainer}>
                <Text style={styles.logoText}>GermAyori</Text>
                <Text style={styles.logoSubtext}>Deja que la IA lea{'\n'}tus gráficos</Text>
                <TouchableOpacity style={styles.promoButton}>
                  <Text style={styles.promoButtonText}>Pruébalo ahora</Text>
                </TouchableOpacity>
              </View>

              {/* Ideas clave con contraste reducido */}
              <View style={styles.ideasContainer}>
                <Text style={styles.ideasTitle}>Ideas clave</Text>
                
                <View style={styles.ideasGrid}>
                  <View style={styles.ideaCard}>
                    <Ionicons name="trending-up" size={20} color="rgba(0,255,136,0.6)" />
                    <Text style={styles.ideaLabel}>Tendencia</Text>
                    <Text style={styles.ideaValue}>Alcista</Text>
                  </View>

                  <View style={styles.ideaCard}>
                    <Ionicons name="pulse" size={20} color="rgba(255,107,53,0.6)" />
                    <Text style={styles.ideaLabel}>Volatilidad</Text>
                    <Text style={styles.ideaValue}>Alto</Text>
                  </View>

                  <View style={styles.ideaCardLarge}>
                    <Ionicons name="bar-chart" size={20} color="rgba(74,144,226,0.6)" />
                    <Text style={styles.ideaLabel}>Volumen</Text>
                    <Text style={styles.ideaValue}>Medio</Text>
                  </View>
                </View>
              </View>

              {/* Área táctil para agregar análisis */}
              <TouchableOpacity 
                style={styles.touchableArea}
                onPress={handleImagePress}
                activeOpacity={0.8}
              >
                <View style={styles.addAnalysisHint}>
                  <Ionicons name="add-circle" size={24} color="rgba(255,215,0,0.8)" />
                  <Text style={styles.hintText}>Toca para agregar análisis</Text>
                </View>
              </TouchableOpacity>

              {/* Análisis guardados superpuestos */}
              {savedAnalyses.map((analysis) => (
                <TouchableOpacity
                  key={analysis.id}
                  style={[
                    styles.analysisPin,
                    {
                      left: analysis.position.x - 15,
                      top: analysis.position.y - 15,
                      borderColor: getSignalColor(analysis.signal)
                    }
                  ]}
                  onLongPress={() => deleteAnalysis(analysis.id)}
                >
                  <Ionicons 
                    name={getSignalIcon(analysis.signal) as any} 
                    size={16} 
                    color={getSignalColor(analysis.signal)} 
                  />
                  <View style={styles.analysisTooltip}>
                    <Text style={styles.tooltipTitle}>{analysis.title}</Text>
                    <Text style={styles.tooltipSymbol}>{analysis.symbol}</Text>
                    <Text style={styles.tooltipConfidence}>{analysis.confidence}% confianza</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </LinearGradient>
          </View>
        </View>

        {/* Lista de análisis guardados */}
        <ThemedView style={styles.analysesList}>
          <ThemedText style={styles.listTitle}>Análisis Guardados ({savedAnalyses.length})</ThemedText>
          
          {savedAnalyses.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="bookmark-outline" size={48} color={colors.secondary} />
              <ThemedText style={[styles.emptyText, { color: colors.secondary }]}>
                No tienes análisis guardados aún
              </ThemedText>
              <ThemedText style={[styles.emptySubtext, { color: colors.secondary }]}>
                Toca la imagen de GermAyori para agregar tu primer análisis
              </ThemedText>
            </View>
          ) : (
            savedAnalyses.map((analysis) => (
              <View key={analysis.id} style={[styles.analysisCard, { backgroundColor: colors.card }]}>
                <View style={styles.analysisHeader}>
                  <View style={styles.analysisInfo}>
                    <ThemedText style={styles.analysisTitle}>{analysis.title}</ThemedText>
                    <ThemedText style={[styles.analysisSymbol, { color: colors.secondary }]}>
                      {analysis.symbol}
                    </ThemedText>
                  </View>
                  <View style={[styles.signalBadge, { backgroundColor: getSignalColor(analysis.signal) }]}>
                    <Ionicons name={getSignalIcon(analysis.signal) as any} size={16} color="#FFFFFF" />
                    <Text style={styles.signalText}>{analysis.signal.toUpperCase()}</Text>
                  </View>
                </View>
                
                {analysis.description && (
                  <ThemedText style={[styles.analysisDescription, { color: colors.secondary }]}>
                    {analysis.description}
                  </ThemedText>
                )}
                
                <View style={styles.analysisFooter}>
                  <ThemedText style={[styles.confidenceText, { color: colors.secondary }]}>
                    Confianza: {analysis.confidence}%
                  </ThemedText>
                  <ThemedText style={[styles.timestampText, { color: colors.secondary }]}>
                    {new Date(analysis.timestamp).toLocaleDateString()}
                  </ThemedText>
                </View>
              </View>
            ))
          )}
        </ThemedView>
      </ScrollView>

      {/* Modal para agregar análisis */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Nuevo Análisis</ThemedText>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <TextInput
              style={[styles.input, { backgroundColor: colors.card, color: colors.text }]}
              placeholder="Título del análisis"
              placeholderTextColor={colors.secondary}
              value={newAnalysis.title}
              onChangeText={(text) => setNewAnalysis({ ...newAnalysis, title: text })}
            />

            <TextInput
              style={[styles.textArea, { backgroundColor: colors.card, color: colors.text }]}
              placeholder="Descripción (opcional)"
              placeholderTextColor={colors.secondary}
              value={newAnalysis.description}
              onChangeText={(text) => setNewAnalysis({ ...newAnalysis, description: text })}
              multiline
              numberOfLines={3}
            />

            <View style={styles.signalSelector}>
              {(['buy', 'sell', 'hold'] as const).map((signal) => (
                <TouchableOpacity
                  key={signal}
                  style={[
                    styles.signalOption,
                    {
                      backgroundColor: newAnalysis.signal === signal 
                        ? getSignalColor(signal) 
                        : colors.card
                    }
                  ]}
                  onPress={() => setNewAnalysis({ ...newAnalysis, signal })}
                >
                  <Ionicons 
                    name={getSignalIcon(signal) as any} 
                    size={20} 
                    color={newAnalysis.signal === signal ? '#FFFFFF' : colors.text} 
                  />
                  <Text style={[
                    styles.signalOptionText,
                    { color: newAnalysis.signal === signal ? '#FFFFFF' : colors.text }
                  ]}>
                    {signal.toUpperCase()}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: '#FFD700' }]}
              onPress={saveAnalysis}
            >
              <Text style={styles.saveButtonText}>Guardar Análisis</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  germAyoriContainer: {
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  germAyoriBackground: {
    height: 400,
    backgroundColor: '#000000',
  },
  imageOverlay: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  logoContainer: {
    alignItems: 'center',
    opacity: 0.6, // Contraste reducido
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFD700',
    textAlign: 'center',
    letterSpacing: 1,
  },
  logoSubtext: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 10,
    opacity: 0.8,
  },
  promoButton: {
    backgroundColor: 'rgba(255, 215, 0, 0.3)',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 15,
    marginTop: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.5)',
  },
  promoButtonText: {
    color: '#FFD700',
    fontSize: 14,
    fontWeight: 'bold',
  },
  ideasContainer: {
    opacity: 0.5, // Contraste reducido
  },
  ideasTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
    textAlign: 'center',
  },
  ideasGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 10,
  },
  ideaCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 10,
    padding: 10,
    width: (width - 60) / 2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  ideaCardLarge: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 10,
    padding: 10,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    marginTop: 5,
  },
  ideaLabel: {
    fontSize: 10,
    color: '#CCCCCC',
    marginTop: 5,
  },
  ideaValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 2,
  },
  touchableArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addAnalysisHint: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.5)',
  },
  hintText: {
    color: '#FFD700',
    fontSize: 12,
    marginTop: 5,
    fontWeight: '600',
  },
  analysisPin: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  analysisTooltip: {
    position: 'absolute',
    top: -60,
    left: -40,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    padding: 8,
    borderRadius: 8,
    minWidth: 120,
  },
  tooltipTitle: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  tooltipSymbol: {
    color: '#FFD700',
    fontSize: 10,
  },
  tooltipConfidence: {
    color: '#CCCCCC',
    fontSize: 10,
  },
  analysesList: {
    marginBottom: 20,
  },
  listTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 15,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
  },
  analysisCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  analysisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  analysisInfo: {
    flex: 1,
  },
  analysisTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  analysisSymbol: {
    fontSize: 14,
    marginTop: 2,
  },
  signalBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  signalText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  analysisDescription: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  analysisFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confidenceText: {
    fontSize: 12,
  },
  timestampText: {
    fontSize: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  input: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
  },
  textArea: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
    height: 80,
    textAlignVertical: 'top',
  },
  signalSelector: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 20,
  },
  signalOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 5,
  },
  signalOptionText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  saveButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#000000',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
